#############################################################################
# Makefile for building: SchellingModel
# Generated by qmake (3.1) (Qt 5.14.2)
# Project:  ../qt/SchellingModel.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_QML_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -g -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -O3 /W4 -g -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I../qt -I. -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -Ibuild/moc -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-subsystem,windows -mthreads
LIBS        =        D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/libQt5Widgets.a D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/libQt5Gui.a D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/libQt5Core.a  -lmingw32 D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/libqtmain.a -LC:/openssl/lib -LC:/Utils/my_sql/mysql-5.7.25-win32/lib -LC:/Utils/postgresql/pgsql/lib -lshell32 
QMAKE         = D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/qmake.exe
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = cp -f
INSTALL_PROGRAM = cp -f
INSTALL_DIR   = cp -f -R
QINSTALL      = D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/qmake.exe -install qinstall
QINSTALL_PROGRAM = D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/qmake.exe -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
IDC           = idc
IDL           = midl
ZIP           = 
DEF_FILE      = 
RES_FILE      = 
SED           = sed
MOVE          = mv -f

####### Output directory

OBJECTS_DIR   = build/obj/

####### Files

SOURCES       = ../qt/src/main.cpp \
		../qt/src/MainWindow.cpp \
		../qt/src/SchellingModel.cpp \
		../qt/src/GridWidget.cpp \
		../qt/src/ParameterPanel.cpp \
		../qt/src/StatisticsPanel.cpp \
		../qt/src/PerformancePanel.cpp \
		../qt/src/ColorConfig.cpp \
		../qt/src/ControlPanel.cpp build/moc/moc_MainWindow.cpp \
		build/moc/moc_SchellingModel.cpp \
		build/moc/moc_GridWidget.cpp \
		build/moc/moc_ParameterPanel.cpp \
		build/moc/moc_StatisticsPanel.cpp \
		build/moc/moc_PerformancePanel.cpp \
		build/moc/moc_ColorConfig.cpp \
		build/moc/moc_ControlPanel.cpp
OBJECTS       = build/obj/main.o \
		build/obj/MainWindow.o \
		build/obj/SchellingModel.o \
		build/obj/GridWidget.o \
		build/obj/ParameterPanel.o \
		build/obj/StatisticsPanel.o \
		build/obj/PerformancePanel.o \
		build/obj/ColorConfig.o \
		build/obj/ControlPanel.o \
		build/obj/moc_MainWindow.o \
		build/obj/moc_SchellingModel.o \
		build/obj/moc_GridWidget.o \
		build/obj/moc_ParameterPanel.o \
		build/obj/moc_StatisticsPanel.o \
		build/obj/moc_PerformancePanel.o \
		build/obj/moc_ColorConfig.o \
		build/obj/moc_ControlPanel.o

DIST          =  ../qt/src/MainWindow.h \
		../qt/src/SchellingModel.h \
		../qt/src/GridWidget.h \
		../qt/src/ParameterPanel.h \
		../qt/src/StatisticsPanel.h \
		../qt/src/PerformancePanel.h \
		../qt/src/ColorConfig.h \
		../qt/src/ControlPanel.h \
		../qt/src/AgentType.h ../qt/src/main.cpp \
		../qt/src/MainWindow.cpp \
		../qt/src/SchellingModel.cpp \
		../qt/src/GridWidget.cpp \
		../qt/src/ParameterPanel.cpp \
		../qt/src/StatisticsPanel.cpp \
		../qt/src/PerformancePanel.cpp \
		../qt/src/ColorConfig.cpp \
		../qt/src/ControlPanel.cpp
QMAKE_TARGET  = SchellingModel
DESTDIR        = bin/ #avoid trailing-slash linebreak
TARGET         = SchellingModel.exe
DESTDIR_TARGET = bin/SchellingModel.exe

####### Build rules

first: all
all: Makefile.Debug  bin/SchellingModel.exe

bin/SchellingModel.exe: D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/libQt5Widgets.a D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/libQt5Gui.a D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/libQt5Core.a D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/libqtmain.a $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) @object_script.SchellingModel.Debug  $(LIBS)

qmake: FORCE
	@$(QMAKE) -o Makefile.Debug ../qt/SchellingModel.pro -spec win32-g++ CONFIG+=debug CONFIG+=qml_debug

qmake_all: FORCE

dist:
	$(ZIP) SchellingModel.zip $(SOURCES) $(DIST) ../qt/SchellingModel.pro D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/spec_pre.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/qdevice.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/device_config.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/sanitize.conf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/gcc-base.conf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/g++-base.conf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/angle.conf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/win32/windows_vulkan_sdk.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/windows-vulkan.conf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/g++-win32.conf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/windows-desktop.conf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/qconfig.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3danimation.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3danimation_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dcore.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dcore_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dextras.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dextras_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dinput.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dinput_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dlogic.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dlogic_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquick.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquick_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickanimation.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickanimation_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickextras.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickextras_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickinput.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickinput_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickrender.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickrender_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickscene2d.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickscene2d_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3drender.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3drender_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_accessibility_support_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axbase.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axbase_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axcontainer.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axcontainer_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axserver.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axserver_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_bluetooth.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_bluetooth_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_bootstrap_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_concurrent.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_concurrent_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_core.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_core_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_dbus.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_dbus_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_designer.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_designer_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_designercomponents_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_devicediscovery_support_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_edid_support_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_egl_support_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_fb_support_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_fontdatabase_support_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_gamepad.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_gamepad_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_gui.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_gui_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_help.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_help_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_location.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_location_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_multimedia.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_multimedia_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_multimediawidgets.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_multimediawidgets_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_network.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_network_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_nfc.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_nfc_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_opengl.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_opengl_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_openglextensions.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_openglextensions_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_packetprotocol_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_platformcompositor_support_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_positioning.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_positioning_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_positioningquick.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_positioningquick_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_printsupport.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_printsupport_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qml.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qml_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmldebug_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmldevtools_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmlmodels.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmlmodels_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmltest.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmltest_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmlworkerscript.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmlworkerscript_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickcontrols2.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickcontrols2_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickparticles_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickshapes_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quicktemplates2.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quicktemplates2_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickwidgets.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickwidgets_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_remoteobjects.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_remoteobjects_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_repparser.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_repparser_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_scxml.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_scxml_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_sensors.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_sensors_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_serialbus.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_serialbus_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_serialport.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_serialport_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_sql.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_sql_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_svg.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_svg_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_testlib.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_testlib_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_texttospeech.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_texttospeech_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_theme_support_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_uiplugin.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_uitools.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_uitools_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_webchannel.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_webchannel_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_websockets.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_websockets_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_widgets.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_widgets_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_windowsuiautomation_support_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_winextras.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_winextras_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_xml.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_xml_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_xmlpatterns.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_xmlpatterns_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_zlib_private.pri D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/qt_functions.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/qt_config.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++/qmake.conf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/spec_post.prf .qmake.stash D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/exclusive_builds.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/toolchain.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/default_pre.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/win32/default_pre.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/resolve_config.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/exclusive_builds_post.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/default_post.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/build_pass.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/qml_debug.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/precompile_header.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/warn_on.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/qt.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/resources_functions.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/resources.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/moc.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/win32/opengl.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/uic.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/qmake_use.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/file_copies.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/win32/windows.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/testcase_targets.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/exceptions.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/yacc.prf D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/lex.prf ../qt/SchellingModel.pro D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/Qt5Widgets.prl D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/Qt5Gui.prl D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/Qt5Core.prl D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/qtmain.prl    D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/data/dummy.cpp ../qt/src/MainWindow.h ../qt/src/SchellingModel.h ../qt/src/GridWidget.h ../qt/src/ParameterPanel.h ../qt/src/StatisticsPanel.h ../qt/src/PerformancePanel.h ../qt/src/ColorConfig.h ../qt/src/ControlPanel.h ../qt/src/AgentType.h  ../qt/src/main.cpp ../qt/src/MainWindow.cpp ../qt/src/SchellingModel.cpp ../qt/src/GridWidget.cpp ../qt/src/ParameterPanel.cpp ../qt/src/StatisticsPanel.cpp ../qt/src/PerformancePanel.cpp ../qt/src/ColorConfig.cpp ../qt/src/ControlPanel.cpp     

clean: compiler_clean 
	-$(DEL_FILE) build/obj/main.o build/obj/MainWindow.o build/obj/SchellingModel.o build/obj/GridWidget.o build/obj/ParameterPanel.o build/obj/StatisticsPanel.o build/obj/PerformancePanel.o build/obj/ColorConfig.o build/obj/ControlPanel.o build/obj/moc_MainWindow.o build/obj/moc_SchellingModel.o build/obj/moc_GridWidget.o build/obj/moc_ParameterPanel.o build/obj/moc_StatisticsPanel.o build/obj/moc_PerformancePanel.o build/obj/moc_ColorConfig.o build/obj/moc_ControlPanel.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: build/moc/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) build/moc/moc_predefs.h
build/moc/moc_predefs.h: D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -O3 /W4 -g -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o build/moc/moc_predefs.h D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/data/dummy.cpp

compiler_moc_header_make_all: build/moc/moc_MainWindow.cpp build/moc/moc_SchellingModel.cpp build/moc/moc_GridWidget.cpp build/moc/moc_ParameterPanel.cpp build/moc/moc_StatisticsPanel.cpp build/moc/moc_PerformancePanel.cpp build/moc/moc_ColorConfig.cpp build/moc/moc_ControlPanel.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) build/moc/moc_MainWindow.cpp build/moc/moc_SchellingModel.cpp build/moc/moc_GridWidget.cpp build/moc/moc_ParameterPanel.cpp build/moc/moc_StatisticsPanel.cpp build/moc/moc_PerformancePanel.cpp build/moc/moc_ColorConfig.cpp build/moc/moc_ControlPanel.cpp
build/moc/moc_MainWindow.cpp: ../qt/src/MainWindow.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMainWindow \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmainwindow.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHBoxLayout \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSplitter \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsplitter.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QScrollArea \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qscrollarea.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QComboBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcombobox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyle.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabbar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qrubberband.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QPushButton \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QColorDialog \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcolordialog.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdialog.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGridLayout \
		../qt/src/SchellingModel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QElapsedTimer \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qelapsedtimer.h \
		../qt/src/AgentType.h \
		../qt/src/GridWidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPixmap \
		../qt/src/ColorConfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QColor \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMap \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QString \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSettings \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsettings.h \
		../qt/src/ParameterPanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSpinBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QDoubleSpinBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		../qt/src/StatisticsPanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QProgressBar \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		../qt/src/PerformancePanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QCheckBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSlider \
		../qt/src/ControlPanel.h \
		build/moc/moc_predefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/moc.exe
	'D:\Soft\Code\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\moc.exe' $(DEFINES) --include D:/project/own/Simulations/schelling/build-SchellingModel-Desktop_Qt_5_14_2_MinGW_32_bit-Debug/build/moc/moc_predefs.h -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ -ID:/project/own/Simulations/schelling/qt -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -I. -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++ -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32 -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include-fixed -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/i686-w64-mingw32/include ../qt/src/MainWindow.h -o build/moc/moc_MainWindow.cpp

build/moc/moc_SchellingModel.cpp: ../qt/src/SchellingModel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QElapsedTimer \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qelapsedtimer.h \
		../qt/src/AgentType.h \
		build/moc/moc_predefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/moc.exe
	'D:\Soft\Code\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\moc.exe' $(DEFINES) --include D:/project/own/Simulations/schelling/build-SchellingModel-Desktop_Qt_5_14_2_MinGW_32_bit-Debug/build/moc/moc_predefs.h -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ -ID:/project/own/Simulations/schelling/qt -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -I. -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++ -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32 -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include-fixed -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/i686-w64-mingw32/include ../qt/src/SchellingModel.h -o build/moc/moc_SchellingModel.cpp

build/moc/moc_GridWidget.cpp: ../qt/src/GridWidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPixmap \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		../qt/src/SchellingModel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QElapsedTimer \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qelapsedtimer.h \
		../qt/src/AgentType.h \
		../qt/src/ColorConfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QColor \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMap \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QString \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSettings \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsettings.h \
		build/moc/moc_predefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/moc.exe
	'D:\Soft\Code\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\moc.exe' $(DEFINES) --include D:/project/own/Simulations/schelling/build-SchellingModel-Desktop_Qt_5_14_2_MinGW_32_bit-Debug/build/moc/moc_predefs.h -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ -ID:/project/own/Simulations/schelling/qt -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -I. -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++ -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32 -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include-fixed -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/i686-w64-mingw32/include ../qt/src/GridWidget.h -o build/moc/moc_GridWidget.cpp

build/moc/moc_ParameterPanel.cpp: ../qt/src/ParameterPanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSpinBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QDoubleSpinBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGridLayout \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		../qt/src/AgentType.h \
		build/moc/moc_predefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/moc.exe
	'D:\Soft\Code\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\moc.exe' $(DEFINES) --include D:/project/own/Simulations/schelling/build-SchellingModel-Desktop_Qt_5_14_2_MinGW_32_bit-Debug/build/moc/moc_predefs.h -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ -ID:/project/own/Simulations/schelling/qt -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -I. -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++ -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32 -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include-fixed -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/i686-w64-mingw32/include ../qt/src/ParameterPanel.h -o build/moc/moc_ParameterPanel.cpp

build/moc/moc_StatisticsPanel.cpp: ../qt/src/StatisticsPanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QProgressBar \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGridLayout \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		../qt/src/AgentType.h \
		build/moc/moc_predefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/moc.exe
	'D:\Soft\Code\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\moc.exe' $(DEFINES) --include D:/project/own/Simulations/schelling/build-SchellingModel-Desktop_Qt_5_14_2_MinGW_32_bit-Debug/build/moc/moc_predefs.h -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ -ID:/project/own/Simulations/schelling/qt -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -I. -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++ -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32 -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include-fixed -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/i686-w64-mingw32/include ../qt/src/StatisticsPanel.h -o build/moc/moc_StatisticsPanel.cpp

build/moc/moc_PerformancePanel.cpp: ../qt/src/PerformancePanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QCheckBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSlider \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGridLayout \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHBoxLayout \
		../qt/src/AgentType.h \
		build/moc/moc_predefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/moc.exe
	'D:\Soft\Code\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\moc.exe' $(DEFINES) --include D:/project/own/Simulations/schelling/build-SchellingModel-Desktop_Qt_5_14_2_MinGW_32_bit-Debug/build/moc/moc_predefs.h -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ -ID:/project/own/Simulations/schelling/qt -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -I. -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++ -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32 -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include-fixed -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/i686-w64-mingw32/include ../qt/src/PerformancePanel.h -o build/moc/moc_PerformancePanel.cpp

build/moc/moc_ColorConfig.cpp: ../qt/src/ColorConfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QColor \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMap \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QString \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSettings \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsettings.h \
		build/moc/moc_predefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/moc.exe
	'D:\Soft\Code\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\moc.exe' $(DEFINES) --include D:/project/own/Simulations/schelling/build-SchellingModel-Desktop_Qt_5_14_2_MinGW_32_bit-Debug/build/moc/moc_predefs.h -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ -ID:/project/own/Simulations/schelling/qt -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -I. -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++ -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32 -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include-fixed -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/i686-w64-mingw32/include ../qt/src/ColorConfig.h -o build/moc/moc_ColorConfig.cpp

build/moc/moc_ControlPanel.cpp: ../qt/src/ControlPanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QPushButton \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSlider \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHBoxLayout \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		build/moc/moc_predefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/bin/moc.exe
	'D:\Soft\Code\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\moc.exe' $(DEFINES) --include D:/project/own/Simulations/schelling/build-SchellingModel-Desktop_Qt_5_14_2_MinGW_32_bit-Debug/build/moc/moc_predefs.h -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++ -ID:/project/own/Simulations/schelling/qt -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtANGLE -ID:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore -I. -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++ -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/i686-w64-mingw32 -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include/c++/backward -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/lib/gcc/i686-w64-mingw32/7.3.0/include-fixed -ID:/Soft/Code/Qt/Qt5.14.2/Tools/mingw730_32/i686-w64-mingw32/include ../qt/src/ControlPanel.h -o build/moc/moc_ControlPanel.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all:
compiler_uic_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean 



####### Compile

build/obj/main.o: ../qt/src/main.cpp D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QApplication \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QStyleFactory \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstylefactory.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDir \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdir.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfileinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDebug \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMessageBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmessagebox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdialog.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSplashScreen \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsplashscreen.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPixmap \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		../qt/src/MainWindow.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMainWindow \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmainwindow.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHBoxLayout \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSplitter \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsplitter.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QScrollArea \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qscrollarea.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QComboBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcombobox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyle.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabbar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qrubberband.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QPushButton \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QColorDialog \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcolordialog.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGridLayout \
		../qt/src/SchellingModel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QElapsedTimer \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qelapsedtimer.h \
		../qt/src/AgentType.h \
		../qt/src/GridWidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		../qt/src/ColorConfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QColor \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMap \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QString \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSettings \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsettings.h \
		../qt/src/ParameterPanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSpinBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QDoubleSpinBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		../qt/src/StatisticsPanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QProgressBar \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		../qt/src/PerformancePanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QCheckBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSlider \
		../qt/src/ControlPanel.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/main.o ../qt/src/main.cpp

build/obj/MainWindow.o: ../qt/src/MainWindow.cpp ../qt/src/MainWindow.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QMainWindow \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qmainwindow.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHBoxLayout \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSplitter \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsplitter.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QScrollArea \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qscrollarea.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractscrollarea.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QComboBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcombobox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractitemdelegate.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyleoption.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qstyle.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtabbar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qrubberband.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qabstractitemmodel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QPushButton \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QColorDialog \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcolordialog.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdialog.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGridLayout \
		../qt/src/SchellingModel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QElapsedTimer \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qelapsedtimer.h \
		../qt/src/AgentType.h \
		../qt/src/GridWidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPixmap \
		../qt/src/ColorConfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QColor \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMap \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QString \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSettings \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsettings.h \
		../qt/src/ParameterPanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSpinBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QDoubleSpinBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		../qt/src/StatisticsPanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QProgressBar \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		../qt/src/PerformancePanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QCheckBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSlider \
		../qt/src/ControlPanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QApplication \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/MainWindow.o ../qt/src/MainWindow.cpp

build/obj/SchellingModel.o: ../qt/src/SchellingModel.cpp ../qt/src/SchellingModel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QElapsedTimer \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qelapsedtimer.h \
		../qt/src/AgentType.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDebug \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QRandomGenerator \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrandom.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/SchellingModel.o ../qt/src/SchellingModel.cpp

build/obj/GridWidget.o: ../qt/src/GridWidget.cpp ../qt/src/GridWidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QWidget \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPainter \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainter.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtextoption.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpen.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPixmap \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QTimer \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtimer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasictimer.h \
		../qt/src/SchellingModel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QVector \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QElapsedTimer \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qelapsedtimer.h \
		../qt/src/AgentType.h \
		../qt/src/ColorConfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QColor \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMap \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QString \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSettings \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsettings.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QPaintEvent \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QResizeEvent \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDebug \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QImage
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/GridWidget.o ../qt/src/GridWidget.cpp

build/obj/ParameterPanel.o: ../qt/src/ParameterPanel.cpp ../qt/src/ParameterPanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSpinBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qspinbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractspinbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvalidator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregularexpression.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QDoubleSpinBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGridLayout \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QVBoxLayout \
		../qt/src/AgentType.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QLocale
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/ParameterPanel.o ../qt/src/ParameterPanel.cpp

build/obj/StatisticsPanel.o: ../qt/src/StatisticsPanel.cpp ../qt/src/StatisticsPanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QProgressBar \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qprogressbar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGridLayout \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		../qt/src/AgentType.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/StatisticsPanel.o ../qt/src/StatisticsPanel.cpp

build/obj/PerformancePanel.o: ../qt/src/PerformancePanel.cpp ../qt/src/PerformancePanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QCheckBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qcheckbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSlider \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGridLayout \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHBoxLayout \
		../qt/src/AgentType.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QWidget
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/PerformancePanel.o ../qt/src/PerformancePanel.cpp

build/obj/ColorConfig.o: ../qt/src/ColorConfig.cpp ../qt/src/ColorConfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QObject \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/QColor \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QMap \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QString \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QSettings \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsettings.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QApplication \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qapplication.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreapplication.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qeventloop.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qdesktopwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qguiapplication.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qinputmethod.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/ColorConfig.o ../qt/src/ColorConfig.cpp

build/obj/ControlPanel.o: ../qt/src/ControlPanel.cpp ../qt/src/ControlPanel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QGroupBox \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgroupbox.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgetsglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtguiglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobal.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig-bootstrapped.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qconfig.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtcore-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsystemdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qprocessordetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcompilerdetection.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtypeinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsysinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlogging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qflags.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbasicatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_bootstrap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qgenericatomic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_cxx11.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qatomic_msvc.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qglobalstatic.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmutex.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnumeric.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qversiontagging.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtgui-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qtwidgets-config.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qframe.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qwidget.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qnamespace.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobjectdefs_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qwindowdefs_win.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstring.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qchar.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrefcount.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qarraydata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringliteral.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringview.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringbuilder.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qalgorithms.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiterator.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhashfunctions.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpair.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvector.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainertools_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qpoint.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qbytearraylist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringlist.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qregexp.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qstringmatcher.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcoreevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qscopedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmetatype.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvarlengtharray.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontainerfwd.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qobject_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmargins.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpaintdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qrect.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsize.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpalette.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcolor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgb.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qrgba64.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qbrush.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qmatrix.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpolygon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qregion.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdatastream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qiodevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qline.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtransform.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpainterpath.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qimage.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixelformat.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qpixmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qshareddata.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qhash.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qsharedpointer_impl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfont.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontmetrics.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qfontinfo.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qsizepolicy.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qcursor.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qkeysequence.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qevent.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qvariant.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qmap.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qdebug.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qtextstream.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qlocale.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qset.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qcontiguouscache.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurl.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qurlquery.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfile.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtCore/qfiledevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qvector2d.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qtouchdevice.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QPushButton \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qpushbutton.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractbutton.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtGui/qicon.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QSlider \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qslider.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qabstractslider.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QLabel \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlabel.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QHBoxLayout \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qboxlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qlayoutitem.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/qgridlayout.h \
		D:/Soft/Code/Qt/Qt5.14.2/5.14.2/mingw73_32/include/QtWidgets/QVBoxLayout
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/ControlPanel.o ../qt/src/ControlPanel.cpp

build/obj/moc_MainWindow.o: build/moc/moc_MainWindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/moc_MainWindow.o build/moc/moc_MainWindow.cpp

build/obj/moc_SchellingModel.o: build/moc/moc_SchellingModel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/moc_SchellingModel.o build/moc/moc_SchellingModel.cpp

build/obj/moc_GridWidget.o: build/moc/moc_GridWidget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/moc_GridWidget.o build/moc/moc_GridWidget.cpp

build/obj/moc_ParameterPanel.o: build/moc/moc_ParameterPanel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/moc_ParameterPanel.o build/moc/moc_ParameterPanel.cpp

build/obj/moc_StatisticsPanel.o: build/moc/moc_StatisticsPanel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/moc_StatisticsPanel.o build/moc/moc_StatisticsPanel.cpp

build/obj/moc_PerformancePanel.o: build/moc/moc_PerformancePanel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/moc_PerformancePanel.o build/moc/moc_PerformancePanel.cpp

build/obj/moc_ColorConfig.o: build/moc/moc_ColorConfig.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/moc_ColorConfig.o build/moc/moc_ColorConfig.cpp

build/obj/moc_ControlPanel.o: build/moc/moc_ControlPanel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o build/obj/moc_ControlPanel.o build/moc/moc_ControlPanel.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

