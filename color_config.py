"""
颜色配置系统
支持默认颜色配置和自定义颜色设置
"""
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QColor
from typing import Dict, Tuple
import json
import os


class ColorConfig(QObject):
    """颜色配置管理类"""
    
    # 颜色配置改变信号
    colors_changed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        
        # 默认颜色配置 - 现代美观配色
        self.default_colors = {
            'empty': (248, 249, 250),               # 空位置 - 浅灰白色
            'type_a_satisfied': (52, 152, 219),     # TYPE_A 满意 - 现代蓝色
            'type_a_unsatisfied': (155, 207, 255),  # TYPE_A 不满意 - 浅蓝色
            'type_b_satisfied': (231, 76, 60),      # TYPE_B 满意 - 现代红色
            'type_b_unsatisfied': (255, 159, 148),  # TYPE_B 不满意 - 浅红色
        }
        
        # 当前颜色配置
        self.colors = self.default_colors.copy()
        
        # 配置文件路径
        self.config_file = "color_config.json"
        
        # 加载保存的配置
        self.load_config()
    
    def get_color(self, color_type: str) -> QColor:
        """获取指定类型的颜色"""
        if color_type in self.colors:
            r, g, b = self.colors[color_type]
            return QColor(r, g, b)
        else:
            # 返回默认白色
            return QColor(255, 255, 255)
    
    def set_color(self, color_type: str, color: Tuple[int, int, int]):
        """设置指定类型的颜色"""
        if color_type in self.colors:
            self.colors[color_type] = color
            self.colors_changed.emit()
    
    def get_all_colors(self) -> Dict[str, QColor]:
        """获取所有颜色的QColor对象"""
        return {
            color_type: self.get_color(color_type) 
            for color_type in self.colors.keys()
        }
    
    def get_color_mapping(self) -> Dict[int, QColor]:
        """获取用于网格渲染的颜色映射"""
        return {
            0: self.get_color('empty'),
            1: self.get_color('type_a_satisfied'),
            2: self.get_color('type_a_unsatisfied'),
            3: self.get_color('type_b_satisfied'),
            4: self.get_color('type_b_unsatisfied'),
        }
    
    def reset_to_default(self):
        """重置为默认颜色"""
        self.colors = self.default_colors.copy()
        self.colors_changed.emit()
    
    def load_preset(self, preset_name: str):
        """加载预设颜色方案"""
        presets = {
            'default': {
                'empty': (248, 249, 250),
                'type_a_satisfied': (52, 152, 219),
                'type_a_unsatisfied': (155, 207, 255),
                'type_b_satisfied': (231, 76, 60),
                'type_b_unsatisfied': (255, 159, 148),
            },
            'ocean': {
                'empty': (245, 248, 250),
                'type_a_satisfied': (26, 188, 156),     # 青绿色
                'type_a_unsatisfied': (130, 224, 170),  # 浅青绿色
                'type_b_satisfied': (52, 73, 94),       # 深蓝灰色
                'type_b_unsatisfied': (149, 165, 166),  # 浅蓝灰色
            },
            'sunset': {
                'empty': (253, 251, 247),
                'type_a_satisfied': (230, 126, 34),     # 橙色
                'type_a_unsatisfied': (243, 156, 18),   # 亮橙色
                'type_b_satisfied': (192, 57, 43),      # 深红色
                'type_b_unsatisfied': (231, 76, 60),    # 红色
            },
            'forest': {
                'empty': (247, 249, 249),
                'type_a_satisfied': (39, 174, 96),      # 绿色
                'type_a_unsatisfied': (88, 214, 141),   # 浅绿色
                'type_b_satisfied': (125, 60, 152),     # 紫色
                'type_b_unsatisfied': (155, 89, 182),   # 浅紫色
            },
            'gradient_blue': {
                'empty': (236, 240, 241),
                'type_a_satisfied': (41, 128, 185),     # 深蓝色
                'type_a_unsatisfied': (133, 193, 233),  # 中蓝色
                'type_b_satisfied': (44, 62, 80),       # 深蓝灰色
                'type_b_unsatisfied': (127, 140, 141),  # 浅蓝灰色
            },
            'warm_earth': {
                'empty': (254, 249, 231),
                'type_a_satisfied': (211, 84, 0),       # 深橙色
                'type_a_unsatisfied': (243, 156, 18),   # 橙色
                'type_b_satisfied': (120, 40, 31),      # 深棕色
                'type_b_unsatisfied': (185, 119, 14),   # 棕色
            },
            'classic': {
                'empty': (255, 255, 255),
                'type_a_satisfied': (0, 0, 255),
                'type_a_unsatisfied': (100, 100, 255),
                'type_b_satisfied': (255, 0, 0),
                'type_b_unsatisfied': (255, 100, 100),
            },
            'colorblind_friendly': {
                'empty': (255, 255, 255),
                'type_a_satisfied': (0, 114, 178),      # 蓝色
                'type_a_unsatisfied': (86, 180, 233),   # 浅蓝色
                'type_b_satisfied': (213, 94, 0),       # 橙色
                'type_b_unsatisfied': (240, 228, 66),   # 黄色
            }
        }
        
        if preset_name in presets:
            self.colors = presets[preset_name].copy()
            self.colors_changed.emit()
    
    def save_config(self):
        """保存颜色配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.colors, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存颜色配置失败: {e}")
    
    def load_config(self):
        """从文件加载颜色配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_colors = json.load(f)
                    # 只更新存在的颜色类型
                    for color_type, color_value in saved_colors.items():
                        if color_type in self.colors:
                            self.colors[color_type] = tuple(color_value)
        except Exception as e:
            print(f"加载颜色配置失败: {e}")
    
    def get_color_names(self) -> Dict[str, str]:
        """获取颜色类型的中文名称"""
        return {
            'empty': '空位置',
            'type_a_satisfied': '少数群体(满意)',
            'type_a_unsatisfied': '少数群体(不满意)',
            'type_b_satisfied': '多数群体(满意)',
            'type_b_unsatisfied': '多数群体(不满意)',
        }
    
    def get_preset_names(self) -> Dict[str, str]:
        """获取预设方案的中文名称"""
        return {
            'default': '现代风格',
            'ocean': '海洋风格',
            'sunset': '日落风格',
            'forest': '森林风格',
            'gradient_blue': '蓝色渐变',
            'warm_earth': '暖色大地',
            'classic': '经典配色',
            'colorblind_friendly': '色盲友好',
        }


# 全局颜色配置实例
color_config = ColorConfig()
