"""
颜色配置系统
支持默认颜色配置和自定义颜色设置
"""
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QColor
from typing import Dict, Tuple
import json
import os


class ColorConfig(QObject):
    """颜色配置管理类"""
    
    # 颜色配置改变信号
    colors_changed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        
        # 默认颜色配置 - 使用淡红色和淡蓝色
        self.default_colors = {
            'empty': (255, 255, 255),           # 空位置 - 白色
            'type_a_satisfied': (173, 216, 230),    # TYPE_A 满意 - 淡蓝色
            'type_a_unsatisfied': (135, 206, 250),  # TYPE_A 不满意 - 天蓝色
            'type_b_satisfied': (255, 182, 193),    # TYPE_B 满意 - 淡粉红色
            'type_b_unsatisfied': (255, 160, 122),  # TYPE_B 不满意 - 淡珊瑚色
        }
        
        # 当前颜色配置
        self.colors = self.default_colors.copy()
        
        # 配置文件路径
        self.config_file = "color_config.json"
        
        # 加载保存的配置
        self.load_config()
    
    def get_color(self, color_type: str) -> QColor:
        """获取指定类型的颜色"""
        if color_type in self.colors:
            r, g, b = self.colors[color_type]
            return QColor(r, g, b)
        else:
            # 返回默认白色
            return QColor(255, 255, 255)
    
    def set_color(self, color_type: str, color: Tuple[int, int, int]):
        """设置指定类型的颜色"""
        if color_type in self.colors:
            self.colors[color_type] = color
            self.colors_changed.emit()
    
    def get_all_colors(self) -> Dict[str, QColor]:
        """获取所有颜色的QColor对象"""
        return {
            color_type: self.get_color(color_type) 
            for color_type in self.colors.keys()
        }
    
    def get_color_mapping(self) -> Dict[int, QColor]:
        """获取用于网格渲染的颜色映射"""
        return {
            0: self.get_color('empty'),
            1: self.get_color('type_a_satisfied'),
            2: self.get_color('type_a_unsatisfied'),
            3: self.get_color('type_b_satisfied'),
            4: self.get_color('type_b_unsatisfied'),
        }
    
    def reset_to_default(self):
        """重置为默认颜色"""
        self.colors = self.default_colors.copy()
        self.colors_changed.emit()
    
    def load_preset(self, preset_name: str):
        """加载预设颜色方案"""
        presets = {
            'default': {
                'empty': (255, 255, 255),
                'type_a_satisfied': (173, 216, 230),
                'type_a_unsatisfied': (135, 206, 250),
                'type_b_satisfied': (255, 182, 193),
                'type_b_unsatisfied': (255, 160, 122),
            },
            'classic': {
                'empty': (255, 255, 255),
                'type_a_satisfied': (0, 0, 255),
                'type_a_unsatisfied': (100, 100, 255),
                'type_b_satisfied': (255, 0, 0),
                'type_b_unsatisfied': (255, 100, 100),
            },
            'high_contrast': {
                'empty': (255, 255, 255),
                'type_a_satisfied': (0, 0, 139),
                'type_a_unsatisfied': (70, 130, 180),
                'type_b_satisfied': (139, 0, 0),
                'type_b_unsatisfied': (220, 20, 60),
            },
            'pastel': {
                'empty': (255, 255, 255),
                'type_a_satisfied': (176, 196, 222),
                'type_a_unsatisfied': (152, 251, 152),
                'type_b_satisfied': (255, 218, 185),
                'type_b_unsatisfied': (255, 192, 203),
            },
            'colorblind_friendly': {
                'empty': (255, 255, 255),
                'type_a_satisfied': (0, 114, 178),
                'type_a_unsatisfied': (86, 180, 233),
                'type_b_satisfied': (213, 94, 0),
                'type_b_unsatisfied': (240, 228, 66),
            }
        }
        
        if preset_name in presets:
            self.colors = presets[preset_name].copy()
            self.colors_changed.emit()
    
    def save_config(self):
        """保存颜色配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.colors, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存颜色配置失败: {e}")
    
    def load_config(self):
        """从文件加载颜色配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_colors = json.load(f)
                    # 只更新存在的颜色类型
                    for color_type, color_value in saved_colors.items():
                        if color_type in self.colors:
                            self.colors[color_type] = tuple(color_value)
        except Exception as e:
            print(f"加载颜色配置失败: {e}")
    
    def get_color_names(self) -> Dict[str, str]:
        """获取颜色类型的中文名称"""
        return {
            'empty': '空位置',
            'type_a_satisfied': '少数群体(满意)',
            'type_a_unsatisfied': '少数群体(不满意)',
            'type_b_satisfied': '多数群体(满意)',
            'type_b_unsatisfied': '多数群体(不满意)',
        }
    
    def get_preset_names(self) -> Dict[str, str]:
        """获取预设方案的中文名称"""
        return {
            'default': '默认(淡色)',
            'classic': '经典',
            'high_contrast': '高对比度',
            'pastel': '柔和色彩',
            'colorblind_friendly': '色盲友好',
        }


# 全局颜色配置实例
color_config = ColorConfig()
