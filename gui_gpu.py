"""
GPU加速版本的PyQt5 GUI
优化了渲染性能和用户体验
"""
import sys
import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QGridLayout, QLabel, QSlider,
                             QPushButton, QSpinBox, QDoubleSpinBox, QGroupBox,
                             QFrame, QSizePolicy, QCheckBox, QProgressBar,
                             QComboBox, QColorDialog)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QPainter, QColor, QFont, QPixmap, QImage
from schelling_model_gpu import SchellingModelGPU
from color_config import color_config


class OptimizedGridWidget(QWidget):
    """优化的网格显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.model = None
        self.cell_size = 8
        self.cached_image = None
        self.needs_redraw = True
        self.setMinimumSize(400, 400)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 连接颜色配置变化信号
        color_config.colors_changed.connect(self.on_colors_changed)

        # 获取颜色映射
        self.update_colors()

    def update_colors(self):
        """更新颜色映射"""
        self.colors = color_config.get_color_mapping()

    def on_colors_changed(self):
        """颜色配置改变时的回调"""
        self.update_colors()
        self.mark_for_redraw()
        self.update()
    
    def set_model(self, model: SchellingModelGPU):
        """设置要显示的模型"""
        self.model = model
        self.needs_redraw = True
        self.update_size()
        self.update()
    
    def update_size(self):
        """根据模型大小更新组件大小"""
        if self.model:
            width = self.model.width * self.cell_size
            height = self.model.height * self.cell_size
            self.setMinimumSize(width, height)
    
    def create_grid_image(self):
        """创建网格图像（缓存优化）"""
        if not self.model or not self.needs_redraw:
            return
        
        # 获取网格数据
        grid_data = self.model.get_grid_cpu()
        satisfaction_data = self.model.get_satisfaction_cpu()
        
        # 创建颜色映射数组
        color_grid = np.zeros((self.model.height, self.model.width), dtype=np.uint8)
        
        # 映射颜色索引
        empty_mask = grid_data == 0
        type_a_satisfied = (grid_data == 1) & satisfaction_data
        type_a_unsatisfied = (grid_data == 1) & (~satisfaction_data)
        type_b_satisfied = (grid_data == 2) & satisfaction_data
        type_b_unsatisfied = (grid_data == 2) & (~satisfaction_data)
        
        color_grid[empty_mask] = 0
        color_grid[type_a_satisfied] = 1
        color_grid[type_a_unsatisfied] = 2
        color_grid[type_b_satisfied] = 3
        color_grid[type_b_unsatisfied] = 4
        
        # 创建RGB图像
        height, width = color_grid.shape
        rgb_array = np.zeros((height, width, 3), dtype=np.uint8)
        
        for color_idx, color in self.colors.items():
            mask = color_grid == color_idx
            rgb_array[mask] = [color.red(), color.green(), color.blue()]
        
        # 创建QImage
        qimage = QImage(rgb_array.data, width, height, width * 3, QImage.Format_RGB888)
        
        # 缩放到显示大小
        widget_width = self.width()
        widget_height = self.height()
        self.cached_image = QPixmap.fromImage(qimage).scaled(
            widget_width, widget_height, Qt.KeepAspectRatio, Qt.FastTransformation
        )
        
        self.needs_redraw = False
    
    def paintEvent(self, event):
        """绘制网格"""
        if not self.model:
            return
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing, False)  # 禁用抗锯齿以提高性能
        
        # 创建或使用缓存的图像
        self.create_grid_image()
        
        if self.cached_image:
            painter.drawPixmap(0, 0, self.cached_image)
    
    def resizeEvent(self, event):
        """窗口大小改变时重新绘制"""
        self.needs_redraw = True
        super().resizeEvent(event)
    
    def mark_for_redraw(self):
        """标记需要重绘"""
        self.needs_redraw = True


class ColorConfigPanel(QGroupBox):
    """颜色配置面板"""

    def __init__(self, parent=None):
        super().__init__("颜色配置", parent)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # 预设方案选择
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel("预设方案:"))

        self.preset_combo = QComboBox()
        preset_names = color_config.get_preset_names()
        for key, name in preset_names.items():
            self.preset_combo.addItem(name, key)
        self.preset_combo.currentIndexChanged.connect(self.on_preset_changed)
        preset_layout.addWidget(self.preset_combo)

        layout.addLayout(preset_layout)

        # 颜色按钮
        colors_layout = QGridLayout()
        color_names = color_config.get_color_names()

        self.color_buttons = {}
        row = 0
        for color_type, display_name in color_names.items():
            label = QLabel(display_name + ":")
            button = QPushButton()
            button.setFixedSize(40, 25)
            button.clicked.connect(lambda checked, ct=color_type: self.choose_color(ct))

            colors_layout.addWidget(label, row, 0)
            colors_layout.addWidget(button, row, 1)

            self.color_buttons[color_type] = button
            row += 1

        layout.addLayout(colors_layout)

        # 重置按钮
        reset_btn = QPushButton("重置为默认")
        reset_btn.clicked.connect(color_config.reset_to_default)
        layout.addWidget(reset_btn)

        self.setLayout(layout)
        self.update_color_buttons()

        # 连接颜色变化信号
        color_config.colors_changed.connect(self.update_color_buttons)

    def update_color_buttons(self):
        """更新颜色按钮显示"""
        for color_type, button in self.color_buttons.items():
            color = color_config.get_color(color_type)
            button.setStyleSheet(f"background-color: rgb({color.red()}, {color.green()}, {color.blue()})")

    def on_preset_changed(self):
        """预设方案改变"""
        preset_key = self.preset_combo.currentData()
        if preset_key:
            color_config.load_preset(preset_key)

    def choose_color(self, color_type):
        """选择颜色"""
        current_color = color_config.get_color(color_type)
        color = QColorDialog.getColor(current_color, self, f"选择{color_config.get_color_names()[color_type]}颜色")
        if color.isValid():
            color_config.set_color(color_type, (color.red(), color.green(), color.blue()))


class PerformancePanel(QGroupBox):
    """性能监控面板"""
    
    def __init__(self, parent=None):
        super().__init__("性能监控", parent)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QGridLayout()
        
        # 性能指标
        self.fps_label = QLabel("FPS: 0.0")
        self.step_time_label = QLabel("步骤时间: 0.000s")
        self.backend_label = QLabel("计算后端: CPU")
        self.gpu_status_label = QLabel("GPU状态: 检测中...")
        
        layout.addWidget(self.fps_label, 0, 0)
        layout.addWidget(self.step_time_label, 1, 0)
        layout.addWidget(self.backend_label, 2, 0)
        layout.addWidget(self.gpu_status_label, 3, 0)
        
        # GPU切换选项
        self.gpu_checkbox = QCheckBox("使用GPU加速")
        self.gpu_checkbox.setChecked(True)
        layout.addWidget(self.gpu_checkbox, 4, 0)
        
        self.setLayout(layout)
    
    def update_performance(self, stats):
        """更新性能信息"""
        self.fps_label.setText(f"FPS: {stats['fps']:.1f}")
        self.step_time_label.setText(f"步骤时间: {stats['last_step_time']:.3f}s")
        backend = "GPU" if stats['using_gpu'] else "CPU"
        self.backend_label.setText(f"计算后端: {backend}")
        
        # 更新GPU状态
        try:
            import cupy as cp
            gpu_info = f"GPU可用: {cp.cuda.runtime.getDeviceCount()}个设备"
        except ImportError:
            gpu_info = "GPU不可用: 未安装CuPy"
        
        self.gpu_status_label.setText(gpu_info)


class EnhancedParameterPanel(QGroupBox):
    """增强的参数配置面板"""
    
    parameters_changed = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__("参数配置", parent)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QGridLayout()
        
        # 网格大小
        layout.addWidget(QLabel("网格宽度:"), 0, 0)
        self.width_spinbox = QSpinBox()
        self.width_spinbox.setRange(10, 200)  # 支持更大的网格
        self.width_spinbox.setValue(100)
        self.width_spinbox.valueChanged.connect(self.parameters_changed.emit)
        layout.addWidget(self.width_spinbox, 0, 1)
        
        layout.addWidget(QLabel("网格高度:"), 1, 0)
        self.height_spinbox = QSpinBox()
        self.height_spinbox.setRange(10, 200)
        self.height_spinbox.setValue(100)
        self.height_spinbox.valueChanged.connect(self.parameters_changed.emit)
        layout.addWidget(self.height_spinbox, 1, 1)
        
        # 人口密度
        layout.addWidget(QLabel("人口密度:"), 2, 0)
        self.density_spinbox = QDoubleSpinBox()
        self.density_spinbox.setRange(0.1, 1.0)
        self.density_spinbox.setSingleStep(0.1)
        self.density_spinbox.setValue(0.8)
        self.density_spinbox.setDecimals(2)
        self.density_spinbox.valueChanged.connect(self.parameters_changed.emit)
        layout.addWidget(self.density_spinbox, 2, 1)
        
        # 少数群体比例
        layout.addWidget(QLabel("少数群体比例:"), 3, 0)
        self.minority_spinbox = QDoubleSpinBox()
        self.minority_spinbox.setRange(0.1, 0.9)
        self.minority_spinbox.setSingleStep(0.1)
        self.minority_spinbox.setValue(0.3)
        self.minority_spinbox.setDecimals(2)
        self.minority_spinbox.valueChanged.connect(self.parameters_changed.emit)
        layout.addWidget(self.minority_spinbox, 3, 1)
        
        # 相似性阈值
        layout.addWidget(QLabel("相似性阈值:"), 4, 0)
        self.homophily_spinbox = QDoubleSpinBox()
        self.homophily_spinbox.setRange(0.0, 1.0)
        self.homophily_spinbox.setSingleStep(0.1)
        self.homophily_spinbox.setValue(0.3)
        self.homophily_spinbox.setDecimals(2)
        self.homophily_spinbox.valueChanged.connect(self.parameters_changed.emit)
        layout.addWidget(self.homophily_spinbox, 4, 1)
        
        self.setLayout(layout)
    
    def get_parameters(self):
        """获取当前参数"""
        return {
            'width': self.width_spinbox.value(),
            'height': self.height_spinbox.value(),
            'density': self.density_spinbox.value(),
            'minority_pc': self.minority_spinbox.value(),
            'homophily': self.homophily_spinbox.value()
        }


class EnhancedStatisticsPanel(QGroupBox):
    """增强的统计信息面板"""
    
    def __init__(self, parent=None):
        super().__init__("统计信息", parent)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QGridLayout()
        
        # 基本统计信息
        self.iteration_label = QLabel("迭代次数: 0")
        self.satisfaction_label = QLabel("总体满意度: 0.00%")
        self.segregation_label = QLabel("隔离指数: 0.00")
        self.agents_label = QLabel("代理总数: 0")
        
        layout.addWidget(self.iteration_label, 0, 0, 1, 2)
        layout.addWidget(self.satisfaction_label, 1, 0, 1, 2)
        layout.addWidget(self.segregation_label, 2, 0, 1, 2)
        layout.addWidget(self.agents_label, 3, 0, 1, 2)
        
        # 分组统计
        layout.addWidget(QLabel("群体统计:"), 4, 0, 1, 2)
        
        self.type_a_count_label = QLabel("少数群体: 0")
        self.type_a_satisfaction_label = QLabel("满意度: 0.00%")
        self.type_b_count_label = QLabel("多数群体: 0")
        self.type_b_satisfaction_label = QLabel("满意度: 0.00%")
        
        layout.addWidget(self.type_a_count_label, 5, 0)
        layout.addWidget(self.type_a_satisfaction_label, 5, 1)
        layout.addWidget(self.type_b_count_label, 6, 0)
        layout.addWidget(self.type_b_satisfaction_label, 6, 1)
        
        # 进度条显示满意度
        layout.addWidget(QLabel("满意度进度:"), 7, 0, 1, 2)
        self.satisfaction_progress = QProgressBar()
        self.satisfaction_progress.setRange(0, 100)
        layout.addWidget(self.satisfaction_progress, 8, 0, 1, 2)
        
        self.setLayout(layout)
    
    def update_statistics(self, stats):
        """更新统计信息显示"""
        self.iteration_label.setText(f"迭代次数: {stats['iteration']}")
        self.satisfaction_label.setText(f"总体满意度: {stats['satisfaction_rate']:.2%}")
        self.segregation_label.setText(f"隔离指数: {stats['segregation_index']:.3f}")
        self.agents_label.setText(f"代理总数: {stats['total_agents']}")
        
        self.type_a_count_label.setText(f"少数群体: {stats['type_a_count']}")
        self.type_a_satisfaction_label.setText(f"满意度: {stats['type_a_satisfaction']:.2%}")
        self.type_b_count_label.setText(f"多数群体: {stats['type_b_count']}")
        self.type_b_satisfaction_label.setText(f"满意度: {stats['type_b_satisfaction']:.2%}")
        
        # 更新进度条
        self.satisfaction_progress.setValue(int(stats['satisfaction_rate'] * 100))


class EnhancedControlPanel(QGroupBox):
    """增强的控制面板"""

    start_simulation = pyqtSignal()
    pause_simulation = pyqtSignal()
    reset_simulation = pyqtSignal()
    speed_changed = pyqtSignal(int)
    gpu_toggled = pyqtSignal(bool)

    def __init__(self, parent=None):
        super().__init__("仿真控制", parent)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # 控制按钮
        button_layout = QHBoxLayout()

        self.start_button = QPushButton("开始")
        self.start_button.clicked.connect(self.start_simulation.emit)
        button_layout.addWidget(self.start_button)

        self.pause_button = QPushButton("暂停")
        self.pause_button.clicked.connect(self.pause_simulation.emit)
        self.pause_button.setEnabled(False)
        button_layout.addWidget(self.pause_button)

        self.reset_button = QPushButton("重置")
        self.reset_button.clicked.connect(self.reset_simulation.emit)
        button_layout.addWidget(self.reset_button)

        layout.addLayout(button_layout)

        # 速度控制
        speed_layout = QHBoxLayout()
        speed_layout.addWidget(QLabel("仿真速度:"))

        self.speed_slider = QSlider(Qt.Horizontal)
        self.speed_slider.setRange(1, 20)  # 更大的速度范围
        self.speed_slider.setValue(10)
        self.speed_slider.valueChanged.connect(self.speed_changed.emit)
        speed_layout.addWidget(self.speed_slider)

        self.speed_label = QLabel("10")
        self.speed_slider.valueChanged.connect(lambda v: self.speed_label.setText(str(v)))
        speed_layout.addWidget(self.speed_label)

        layout.addLayout(speed_layout)

        self.setLayout(layout)

    def set_simulation_running(self, running):
        """设置仿真运行状态"""
        self.start_button.setEnabled(not running)
        self.pause_button.setEnabled(running)


class MainWindowGPU(QMainWindow):
    """GPU加速版本的主窗口"""

    def __init__(self):
        super().__init__()
        self.model = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_simulation)
        self.simulation_running = False

        self.setup_ui()
        self.create_model()

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("Schelling隔离模型仿真 - GPU加速版")
        self.setGeometry(100, 100, 1400, 900)

        # 确保窗口显示在屏幕上
        self.move(50, 50)
        self.resize(1400, 900)
        self.setMinimumSize(1000, 700)

        # 强制窗口置顶并激活
        self.raise_()
        self.activateWindow()

        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QHBoxLayout(central_widget)

        # 左侧面板
        left_panel = QVBoxLayout()

        # 参数面板
        self.parameter_panel = EnhancedParameterPanel()
        self.parameter_panel.parameters_changed.connect(self.on_parameters_changed)
        left_panel.addWidget(self.parameter_panel)

        # 控制面板
        self.control_panel = EnhancedControlPanel()
        self.control_panel.start_simulation.connect(self.start_simulation)
        self.control_panel.pause_simulation.connect(self.pause_simulation)
        self.control_panel.reset_simulation.connect(self.reset_simulation)
        self.control_panel.speed_changed.connect(self.set_simulation_speed)
        left_panel.addWidget(self.control_panel)

        # 统计面板
        self.statistics_panel = EnhancedStatisticsPanel()
        left_panel.addWidget(self.statistics_panel)

        # 性能面板
        self.performance_panel = PerformancePanel()
        self.performance_panel.gpu_checkbox.toggled.connect(self.toggle_gpu)
        left_panel.addWidget(self.performance_panel)

        # 颜色配置面板
        self.color_panel = ColorConfigPanel()
        left_panel.addWidget(self.color_panel)

        left_panel.addStretch()

        # 右侧网格显示
        self.grid_widget = OptimizedGridWidget()

        # 添加到主布局
        left_widget = QWidget()
        left_widget.setLayout(left_panel)
        left_widget.setFixedWidth(350)

        main_layout.addWidget(left_widget)
        main_layout.addWidget(self.grid_widget, 1)

    def create_model(self):
        """创建模型"""
        params = self.parameter_panel.get_parameters()
        use_gpu = self.performance_panel.gpu_checkbox.isChecked()

        print(f"创建模型: {params['width']}x{params['height']}, GPU={use_gpu}")

        self.model = SchellingModelGPU(use_gpu=use_gpu, **params)
        self.grid_widget.set_model(self.model)
        self.update_statistics()

    def on_parameters_changed(self):
        """参数改变时的处理"""
        if not self.simulation_running:
            self.create_model()

    def toggle_gpu(self, use_gpu):
        """切换GPU/CPU模式"""
        if self.model:
            success = self.model.switch_backend(use_gpu)
            if success:
                self.grid_widget.mark_for_redraw()
                self.grid_widget.update()
                self.update_statistics()

    def start_simulation(self):
        """开始仿真"""
        self.simulation_running = True
        self.control_panel.set_simulation_running(True)
        self.timer.start(50)  # 更快的默认速度

    def pause_simulation(self):
        """暂停仿真"""
        self.simulation_running = False
        self.control_panel.set_simulation_running(False)
        self.timer.stop()

    def reset_simulation(self):
        """重置仿真"""
        self.pause_simulation()
        self.model.reset()
        self.grid_widget.mark_for_redraw()
        self.grid_widget.update()
        self.update_statistics()

    def set_simulation_speed(self, speed):
        """设置仿真速度"""
        # 速度1-20，对应500ms-25ms的间隔
        interval = max(25, 525 - speed * 25)
        if self.timer.isActive():
            self.timer.setInterval(interval)

    def update_simulation(self):
        """更新仿真"""
        if self.model.step():
            self.grid_widget.mark_for_redraw()
            self.grid_widget.update()
            self.update_statistics()
        else:
            # 仿真结束（所有代理都满意或无法移动）
            self.pause_simulation()

    def update_statistics(self):
        """更新统计信息"""
        stats = self.model.get_statistics()
        self.statistics_panel.update_statistics(stats)
        self.performance_panel.update_performance(stats)
