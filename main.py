"""
Schelling's Segregation Model - Main Application Entry Point
"""
import sys
from PyQt5.QtWidgets import QApplication
from gui import MainWindow


def main():
    """主函数"""
    print("正在启动Schelling隔离模型仿真...")

    try:
        # 启用高DPI支持
        QApplication.setAttribute(5, True)  # Qt.AA_EnableHighDpiScaling
        QApplication.setAttribute(6, True)  # Qt.AA_UseHighDpiPixmaps

        print("创建QApplication...")
        app = QApplication(sys.argv)

        # 设置应用程序信息
        app.setApplicationName("Schelling隔离模型仿真")
        app.setApplicationVersion("1.0")
        app.setOrganizationName("Simulation Lab")

        print("创建主窗口...")
        # 创建并显示主窗口
        window = MainWindow()
        print("显示主窗口...")
        window.show()

        print("程序界面应该已经显示，开始事件循环...")
        # 运行应用程序
        sys.exit(app.exec_())

    except Exception as e:
        print(f"程序启动时发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("按Enter键退出...")


if __name__ == "__main__":
    main()
    # print("ok"*10)
