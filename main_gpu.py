"""
GPU加速版本的Schelling隔离模型 - 主程序入口
"""
import sys
from PyQt5.QtWidgets import QApplication
from gui_gpu import MainWindowGPU


def main():
    """主函数"""
    print("正在启动GPU加速版Schelling隔离模型仿真...")
    
    try:
        # 启用高DPI支持
        QApplication.setAttribute(5, True)  # Qt.AA_EnableHighDpiScaling
        QApplication.setAttribute(6, True)  # Qt.AA_UseHighDpiPixmaps
        
        print("创建QApplication...")
        app = QApplication(sys.argv)
        
        # 设置应用程序信息
        app.setApplicationName("Schelling隔离模型仿真 - GPU加速版")
        app.setApplicationVersion("2.0")
        app.setOrganizationName("Simulation Lab")
        
        print("创建主窗口...")
        # 创建并显示主窗口
        window = MainWindowGPU()
        print("显示主窗口...")
        window.show()
        
        print("GPU加速版程序界面应该已经显示，开始事件循环...")
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"程序启动时发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("按Enter键退出...")


if __name__ == "__main__":
    main()
