"""
CPU vs GPU性能对比测试
"""
import time
import numpy as np
from schelling_model import SchellingModel
from schelling_model_gpu import SchellingModelGPU


def benchmark_model(model_class, name, **params):
    """测试模型性能"""
    print(f"\n=== {name} 性能测试 ===")
    
    # 创建模型
    start_time = time.time()
    if model_class == SchellingModelGPU:
        model = model_class(use_gpu=True, **params)
    else:
        model = model_class(**params)
    
    init_time = time.time() - start_time
    print(f"初始化时间: {init_time:.3f}s")
    
    # 运行仿真步骤
    step_times = []
    max_steps = 100
    
    print("运行仿真步骤...")
    for step in range(max_steps):
        step_start = time.time()
        
        if not model.step():
            print(f"仿真在第{step}步达到平衡")
            break
        
        step_time = time.time() - step_start
        step_times.append(step_time)
        
        if (step + 1) % 20 == 0:
            avg_time = np.mean(step_times[-20:])
            fps = 1.0 / avg_time if avg_time > 0 else 0
            print(f"步骤 {step + 1}: 平均时间 {avg_time:.4f}s, FPS {fps:.1f}")
    
    # 统计结果
    if step_times:
        avg_step_time = np.mean(step_times)
        min_step_time = np.min(step_times)
        max_step_time = np.max(step_times)
        avg_fps = 1.0 / avg_step_time if avg_step_time > 0 else 0
        
        print(f"\n性能统计:")
        print(f"  平均步骤时间: {avg_step_time:.4f}s")
        print(f"  最快步骤时间: {min_step_time:.4f}s")
        print(f"  最慢步骤时间: {max_step_time:.4f}s")
        print(f"  平均FPS: {avg_fps:.1f}")
        print(f"  总步骤数: {len(step_times)}")
        
        # 获取最终统计
        final_stats = model.get_statistics()
        print(f"  最终满意度: {final_stats['satisfaction_rate']:.2%}")
        print(f"  最终隔离指数: {final_stats['segregation_index']:.3f}")
        
        return {
            'avg_step_time': avg_step_time,
            'avg_fps': avg_fps,
            'total_steps': len(step_times),
            'init_time': init_time,
            'final_satisfaction': final_stats['satisfaction_rate'],
            'final_segregation': final_stats['segregation_index']
        }
    
    return None


def compare_performance():
    """对比CPU和GPU性能"""
    print("Schelling模型 CPU vs GPU 性能对比测试")
    print("=" * 60)
    
    # 测试参数
    test_configs = [
        {
            'name': '小规模测试',
            'params': {'width': 50, 'height': 50, 'density': 0.8, 'minority_pc': 0.3, 'homophily': 0.3}
        },
        {
            'name': '中等规模测试',
            'params': {'width': 100, 'height': 100, 'density': 0.8, 'minority_pc': 0.3, 'homophily': 0.3}
        },
        {
            'name': '大规模测试',
            'params': {'width': 150, 'height': 150, 'density': 0.8, 'minority_pc': 0.3, 'homophily': 0.3}
        }
    ]
    
    results = {}
    
    for config in test_configs:
        print(f"\n{'='*20} {config['name']} {'='*20}")
        print(f"网格大小: {config['params']['width']}x{config['params']['height']}")
        print(f"总代理数: ~{int(config['params']['width'] * config['params']['height'] * config['params']['density'])}")
        
        # 测试CPU版本
        try:
            cpu_result = benchmark_model(SchellingModel, "CPU版本", **config['params'])
            results[f"{config['name']}_CPU"] = cpu_result
        except Exception as e:
            print(f"CPU测试失败: {e}")
            results[f"{config['name']}_CPU"] = None
        
        # 测试GPU版本
        try:
            gpu_result = benchmark_model(SchellingModelGPU, "GPU版本", **config['params'])
            results[f"{config['name']}_GPU"] = gpu_result
        except Exception as e:
            print(f"GPU测试失败: {e}")
            results[f"{config['name']}_GPU"] = None
        
        # 对比结果
        cpu_key = f"{config['name']}_CPU"
        gpu_key = f"{config['name']}_GPU"
        
        if results[cpu_key] and results[gpu_key]:
            cpu_fps = results[cpu_key]['avg_fps']
            gpu_fps = results[gpu_key]['avg_fps']
            speedup = gpu_fps / cpu_fps if cpu_fps > 0 else 0
            
            print(f"\n{config['name']} 对比结果:")
            print(f"  CPU FPS: {cpu_fps:.1f}")
            print(f"  GPU FPS: {gpu_fps:.1f}")
            print(f"  GPU加速比: {speedup:.2f}x")
            
            if speedup > 1:
                print(f"  🚀 GPU比CPU快 {speedup:.1f} 倍!")
            elif speedup < 1:
                print(f"  ⚠️  CPU比GPU快 {1/speedup:.1f} 倍")
            else:
                print(f"  ⚖️  性能相当")
    
    # 总结
    print(f"\n{'='*60}")
    print("性能测试总结:")
    
    for config in test_configs:
        cpu_key = f"{config['name']}_CPU"
        gpu_key = f"{config['name']}_GPU"
        
        if results[cpu_key] and results[gpu_key]:
            speedup = results[gpu_key]['avg_fps'] / results[cpu_key]['avg_fps']
            print(f"  {config['name']}: GPU加速比 {speedup:.2f}x")
    
    # GPU可用性检查
    try:
        import cupy as cp
        device_count = cp.cuda.runtime.getDeviceCount()
        print(f"\nGPU信息: 检测到 {device_count} 个CUDA设备")
        for i in range(device_count):
            with cp.cuda.Device(i):
                meminfo = cp.cuda.runtime.memGetInfo()
                free_mem = meminfo[0] / 1024**3
                total_mem = meminfo[1] / 1024**3
                print(f"  设备 {i}: {total_mem:.1f}GB 总内存, {free_mem:.1f}GB 可用")
    except ImportError:
        print("\n⚠️  CuPy未安装，无法使用GPU加速")
        print("安装命令: pip install cupy-cuda11x  # 或适合您CUDA版本的包")
    except Exception as e:
        print(f"\nGPU检测错误: {e}")


def quick_test():
    """快速测试"""
    print("快速性能测试...")
    
    params = {'width': 80, 'height': 80, 'density': 0.8, 'minority_pc': 0.3, 'homophily': 0.3}
    
    # CPU测试
    print("\nCPU测试 (10步):")
    cpu_model = SchellingModel(**params)
    cpu_times = []
    
    for i in range(10):
        start = time.time()
        cpu_model.step()
        cpu_times.append(time.time() - start)
    
    cpu_avg = np.mean(cpu_times)
    print(f"CPU平均时间: {cpu_avg:.4f}s, FPS: {1/cpu_avg:.1f}")
    
    # GPU测试
    print("\nGPU测试 (10步):")
    try:
        gpu_model = SchellingModelGPU(use_gpu=True, **params)
        gpu_times = []
        
        for i in range(10):
            start = time.time()
            gpu_model.step()
            gpu_times.append(time.time() - start)
        
        gpu_avg = np.mean(gpu_times)
        speedup = cpu_avg / gpu_avg
        print(f"GPU平均时间: {gpu_avg:.4f}s, FPS: {1/gpu_avg:.1f}")
        print(f"加速比: {speedup:.2f}x")
        
    except Exception as e:
        print(f"GPU测试失败: {e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        quick_test()
    else:
        compare_performance()
