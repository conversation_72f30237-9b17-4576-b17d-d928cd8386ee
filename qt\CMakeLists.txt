cmake_minimum_required(VERSION 3.16)
project(SchellingModel VERSION 1.0.0 LANGUAGES CXX CUDA)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required Qt components
find_package(Qt6 REQUIRED COMPONENTS Core Widgets)

# Find CUDA (optional)
find_package(CUDA QUIET)
if(CUDA_FOUND)
    enable_language(CUDA)
    add_definitions(-DUSE_CUDA)
    message(STATUS "CUDA found: ${CUDA_VERSION}")
else()
    message(STATUS "CUDA not found, using CPU-only version")
endif()

# Set Qt MOC, UIC, RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/src)

# Source files
set(SOURCES
    src/main.cpp
    src/MainWindow.cpp
    src/SchellingModel.cpp
    src/GridWidget.cpp
    src/ParameterPanel.cpp
    src/StatisticsPanel.cpp
    src/PerformancePanel.cpp
    src/ColorConfig.cpp
    src/ControlPanel.cpp
)

# Header files
set(HEADERS
    src/MainWindow.h
    src/SchellingModel.h
    src/GridWidget.h
    src/ParameterPanel.h
    src/StatisticsPanel.h
    src/PerformancePanel.h
    src/ColorConfig.h
    src/ControlPanel.h
    src/AgentType.h
)

# CUDA source files (if available)
if(CUDA_FOUND)
    set(CUDA_SOURCES
        src/SchellingModelGPU.cu
        src/CudaKernels.cu
    )
    set_property(SOURCE ${CUDA_SOURCES} PROPERTY LANGUAGE CUDA)
    list(APPEND SOURCES ${CUDA_SOURCES})
    
    set(CUDA_HEADERS
        src/SchellingModelGPU.h
        src/CudaKernels.h
    )
    list(APPEND HEADERS ${CUDA_HEADERS})
endif()

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# Link Qt libraries
target_link_libraries(${PROJECT_NAME} Qt6::Core Qt6::Widgets)

# Link CUDA libraries if available
if(CUDA_FOUND)
    target_link_libraries(${PROJECT_NAME} ${CUDA_LIBRARIES})
    set_property(TARGET ${PROJECT_NAME} PROPERTY CUDA_ARCHITECTURES 60 70 75 80 86)
endif()

# Compiler-specific options
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE /W4)
else()
    target_compile_options(${PROJECT_NAME} PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Set output directory
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Copy resources if any
# file(COPY resources DESTINATION ${CMAKE_BINARY_DIR}/bin)
