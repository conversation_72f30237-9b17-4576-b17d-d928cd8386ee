QT += core widgets

CONFIG += c++17

TARGET = SchellingModel
TEMPLATE = app

# Enable CUDA if available
cuda {
    DEFINES += USE_CUDA
    CUDA_SOURCES += src/SchellingModelGPU.cu src/CudaKernels.cu
    CUDA_HEADERS += src/SchellingModelGPU.h src/CudaKernels.h
    
    # CUDA settings
    CUDA_DIR = $$(CUDA_PATH)
    INCLUDEPATH += $$CUDA_DIR/include
    LIBS += -L$$CUDA_DIR/lib/x64 -lcudart -lcurand
    
    # CUDA compiler settings
    NVCC_OPTIONS = --use_fast_math --ptxas-options=-v
    
    # Define CUDA compilation rule
    cuda.input = CUDA_SOURCES
    cuda.output = ${OBJECTS_DIR}${QMAKE_FILE_BASE}_cuda.o
    cuda.commands = nvcc $$NVCC_OPTIONS -c ${QMAKE_FILE_NAME} -o ${QMAKE_FILE_OUT}
    cuda.dependency_type = TYPE_C
    QMAKE_EXTRA_COMPILERS += cuda
}

SOURCES += \
    src/main.cpp \
    src/MainWindow.cpp \
    src/SchellingModel.cpp \
    src/GridWidget.cpp \
    src/ParameterPanel.cpp \
    src/StatisticsPanel.cpp \
    src/PerformancePanel.cpp \
    src/ColorConfig.cpp \
    src/ControlPanel.cpp

HEADERS += \
    src/MainWindow.h \
    src/SchellingModel.h \
    src/GridWidget.h \
    src/ParameterPanel.h \
    src/StatisticsPanel.h \
    src/PerformancePanel.h \
    src/ColorConfig.h \
    src/ControlPanel.h \
    src/AgentType.h

# Add CUDA headers if available
cuda {
    HEADERS += $$CUDA_HEADERS
}

# Output directory
DESTDIR = bin

# Intermediate directories
OBJECTS_DIR = build/obj
MOC_DIR = build/moc
UI_DIR = build/ui
RCC_DIR = build/rcc

# Compiler flags
QMAKE_CXXFLAGS += -O3
win32:QMAKE_CXXFLAGS += /W4
unix:QMAKE_CXXFLAGS += -Wall -Wextra -Wpedantic

# Resources
# RESOURCES += resources.qrc
