#ifndef AGENTTYPE_H
#define AGENTTYPE_H

/**
 * @brief Agent type enumeration
 */
enum class AgentType : int {
    EMPTY = 0,
    TYPE_A = 1,  // Minority group
    TYPE_B = 2   // Majority group
};

/**
 * @brief Statistics structure for model state
 */
struct ModelStatistics {
    int iteration = 0;
    int totalAgents = 0;
    int satisfiedAgents = 0;
    double satisfactionRate = 0.0;
    double segregationIndex = 0.0;
    int emptyCells = 0;
    
    int typeACount = 0;
    int typeBCount = 0;
    double typeASatisfaction = 0.0;
    double typeBSatisfaction = 0.0;
    
    double densityActual = 0.0;
    double minorityRatioActual = 0.0;
    
    double lastStepTime = 0.0;
    double avgStepTime = 0.0;
    bool usingGPU = false;
    double fps = 0.0;
};

/**
 * @brief Model parameters structure
 */
struct ModelParameters {
    int width = 100;
    int height = 100;
    double density = 0.8;
    double minorityRatio = 0.3;
    double homophily = 0.3;
    bool useGPU = true;
};

#endif // AGENTTYPE_H
