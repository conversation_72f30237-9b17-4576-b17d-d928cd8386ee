#include "ColorConfig.h"
#include <QApplication>
#include <QDebug>

ColorConfig *ColorConfig::s_instance = nullptr;

ColorConfig::ColorConfig(QObject *parent)
    : QObject(parent)
    , m_settings(new QSettings("SchellingModel", "ColorConfig", this))
{
    setDefaultColors();
    initializePresets();
    loadConfig();
}

ColorConfig::~ColorConfig()
{
    saveConfig();
}

ColorConfig *ColorConfig::instance()
{
    if (!s_instance) {
        s_instance = new ColorConfig(qApp);
    }
    return s_instance;
}

void ColorConfig::setDefaultColors()
{
    // Modern style colors (default)
    m_colors[Empty] = QColor(248, 249, 250);
    m_colors[TypeASatisfied] = QColor(52, 152, 219);
    m_colors[TypeAUnsatisfied] = QColor(155, 207, 255);
    m_colors[TypeBSatisfied] = QColor(231, 76, 60);
    m_colors[TypeBUnsatisfied] = QColor(255, 159, 148);
}

void ColorConfig::initializePresets()
{
    // Default (Modern)
    m_presets["default"] = {
        {Empty, QColor(248, 249, 250)},
        {TypeASatisfied, QColor(52, 152, 219)},
        {TypeAUnsatisfied, QColor(155, 207, 255)},
        {TypeBSatisfied, QColor(231, 76, 60)},
        {TypeBUnsatisfied, QColor(255, 159, 148)}
    };
    
    // Ocean style
    m_presets["ocean"] = {
        {Empty, QColor(245, 248, 250)},
        {TypeASatisfied, QColor(26, 188, 156)},
        {TypeAUnsatisfied, QColor(130, 224, 170)},
        {TypeBSatisfied, QColor(52, 73, 94)},
        {TypeBUnsatisfied, QColor(149, 165, 166)}
    };
    
    // Sunset style
    m_presets["sunset"] = {
        {Empty, QColor(253, 251, 247)},
        {TypeASatisfied, QColor(230, 126, 34)},
        {TypeAUnsatisfied, QColor(243, 156, 18)},
        {TypeBSatisfied, QColor(192, 57, 43)},
        {TypeBUnsatisfied, QColor(231, 76, 60)}
    };
    
    // Forest style
    m_presets["forest"] = {
        {Empty, QColor(247, 249, 249)},
        {TypeASatisfied, QColor(39, 174, 96)},
        {TypeAUnsatisfied, QColor(88, 214, 141)},
        {TypeBSatisfied, QColor(125, 60, 152)},
        {TypeBUnsatisfied, QColor(155, 89, 182)}
    };
    
    // Gradient blue
    m_presets["gradient_blue"] = {
        {Empty, QColor(236, 240, 241)},
        {TypeASatisfied, QColor(41, 128, 185)},
        {TypeAUnsatisfied, QColor(133, 193, 233)},
        {TypeBSatisfied, QColor(44, 62, 80)},
        {TypeBUnsatisfied, QColor(127, 140, 141)}
    };
    
    // Warm earth
    m_presets["warm_earth"] = {
        {Empty, QColor(254, 249, 231)},
        {TypeASatisfied, QColor(211, 84, 0)},
        {TypeAUnsatisfied, QColor(243, 156, 18)},
        {TypeBSatisfied, QColor(120, 40, 31)},
        {TypeBUnsatisfied, QColor(185, 119, 14)}
    };
    
    // Classic
    m_presets["classic"] = {
        {Empty, QColor(255, 255, 255)},
        {TypeASatisfied, QColor(0, 0, 255)},
        {TypeAUnsatisfied, QColor(100, 100, 255)},
        {TypeBSatisfied, QColor(255, 0, 0)},
        {TypeBUnsatisfied, QColor(255, 100, 100)}
    };
    
    // Colorblind friendly
    m_presets["colorblind_friendly"] = {
        {Empty, QColor(255, 255, 255)},
        {TypeASatisfied, QColor(0, 114, 178)},
        {TypeAUnsatisfied, QColor(86, 180, 233)},
        {TypeBSatisfied, QColor(213, 94, 0)},
        {TypeBUnsatisfied, QColor(240, 228, 66)}
    };
}

QColor ColorConfig::getColor(ColorType type) const
{
    return m_colors.value(type, QColor(255, 255, 255));
}

void ColorConfig::setColor(ColorType type, const QColor &color)
{
    if (m_colors[type] != color) {
        m_colors[type] = color;
        emit colorsChanged();
    }
}

QMap<int, QColor> ColorConfig::getColorMapping() const
{
    QMap<int, QColor> mapping;
    for (auto it = m_colors.begin(); it != m_colors.end(); ++it) {
        mapping[static_cast<int>(it.key())] = it.value();
    }
    return mapping;
}

void ColorConfig::loadPreset(const QString &presetName)
{
    if (m_presets.contains(presetName)) {
        m_colors = m_presets[presetName];
        emit colorsChanged();
    }
}

void ColorConfig::resetToDefault()
{
    loadPreset("default");
}

QStringList ColorConfig::getPresetNames() const
{
    return m_presets.keys();
}

QString ColorConfig::getPresetDisplayName(const QString &presetName) const
{
    static QMap<QString, QString> displayNames = {
        {"default", "现代风格"},
        {"ocean", "海洋风格"},
        {"sunset", "日落风格"},
        {"forest", "森林风格"},
        {"gradient_blue", "蓝色渐变"},
        {"warm_earth", "暖色大地"},
        {"classic", "经典配色"},
        {"colorblind_friendly", "色盲友好"}
    };
    
    return displayNames.value(presetName, presetName);
}

QString ColorConfig::getColorTypeName(ColorType type) const
{
    static QMap<ColorType, QString> names = {
        {Empty, "空位置"},
        {TypeASatisfied, "少数群体(满意)"},
        {TypeAUnsatisfied, "少数群体(不满意)"},
        {TypeBSatisfied, "多数群体(满意)"},
        {TypeBUnsatisfied, "多数群体(不满意)"}
    };
    
    return names.value(type, "未知");
}

void ColorConfig::saveConfig()
{
    m_settings->beginGroup("Colors");
    for (auto it = m_colors.begin(); it != m_colors.end(); ++it) {
        m_settings->setValue(QString::number(static_cast<int>(it.key())), it.value());
    }
    m_settings->endGroup();
}

void ColorConfig::loadConfig()
{
    m_settings->beginGroup("Colors");
    for (auto it = m_colors.begin(); it != m_colors.end(); ++it) {
        QString key = QString::number(static_cast<int>(it.key()));
        if (m_settings->contains(key)) {
            m_colors[it.key()] = m_settings->value(key).value<QColor>();
        }
    }
    m_settings->endGroup();
}
