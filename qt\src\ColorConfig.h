#ifndef COLORCONFIG_H
#define COLORCONFIG_H

#include <QObject>
#include <QColor>
#include <QMap>
#include <QString>
#include <QSettings>

/**
 * @brief Color configuration manager
 * Manages color schemes for the Schelling model visualization
 */
class ColorConfig : public QObject
{
    Q_OBJECT

public:
    enum ColorType {
        Empty = 0,
        TypeASatisfied = 1,
        TypeAUnsatisfied = 2,
        TypeBSatisfied = 3,
        TypeBUnsatisfied = 4
    };

    explicit ColorConfig(QObject *parent = nullptr);
    ~ColorConfig();

    // Color management
    QColor getColor(ColorType type) const;
    void setColor(ColorType type, const QColor &color);
    QMap<int, QColor> getColorMapping() const;
    
    // Preset management
    void loadPreset(const QString &presetName);
    void resetToDefault();
    QStringList getPresetNames() const;
    QString getPresetDisplayName(const QString &presetName) const;
    
    // Color type names
    QString getColorTypeName(ColorType type) const;
    
    // Persistence
    void saveConfig();
    void loadConfig();

signals:
    void colorsChanged();

private:
    void initializePresets();
    void setDefaultColors();
    
    QMap<ColorType, QColor> m_colors;
    QMap<QString, QMap<ColorType, QColor>> m_presets;
    QSettings *m_settings;
    
    static ColorConfig *s_instance;

public:
    static ColorConfig *instance();
};

// Global convenience function
inline ColorConfig *colorConfig() {
    return ColorConfig::instance();
}

#endif // COLORCONFIG_H
