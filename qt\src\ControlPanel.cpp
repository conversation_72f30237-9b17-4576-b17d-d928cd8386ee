#include "ControlPanel.h"

ControlPanel::ControlPanel(QWidget *parent)
    : QGroupBox("仿真控制", parent)
{
    setupUI();
}

ControlPanel::~ControlPanel() = default;

void ControlPanel::setupUI()
{
    auto *layout = new QVBoxLayout(this);
    
    // Control buttons
    auto *buttonLayout = new QHBoxLayout;
    
    m_startButton = new QPushButton("开始");
    connect(m_startButton, &QPushButton::clicked, this, &ControlPanel::onStartClicked);
    buttonLayout->addWidget(m_startButton);
    
    m_pauseButton = new QPushButton("暂停");
    m_pauseButton->setEnabled(false);
    connect(m_pauseButton, &QPushButton::clicked, this, &ControlPanel::onPauseClicked);
    buttonLayout->addWidget(m_pauseButton);
    
    m_resetButton = new QPushButton("重置");
    connect(m_resetButton, &QPushButton::clicked, this, &ControlPanel::onResetClicked);
    buttonLayout->addWidget(m_resetButton);
    
    layout->addLayout(buttonLayout);
    
    // Speed control
    auto *speedLayout = new QHBoxLayout;
    speedLayout->addWidget(new QLabel("仿真速度:"));
    
    m_speedSlider = new QSlider(Qt::Horizontal);
    m_speedSlider->setRange(1, 20);
    m_speedSlider->setValue(10);
    connect(m_speedSlider, &QSlider::valueChanged, this, &ControlPanel::onSpeedChanged);
    speedLayout->addWidget(m_speedSlider);
    
    m_speedLabel = new QLabel("10");
    speedLayout->addWidget(m_speedLabel);
    
    layout->addLayout(speedLayout);
}

void ControlPanel::setSimulationRunning(bool running)
{
    m_startButton->setEnabled(!running);
    m_pauseButton->setEnabled(running);
}

int ControlPanel::getSpeed() const
{
    return m_speedSlider->value();
}

void ControlPanel::onStartClicked()
{
    emit startSimulation();
}

void ControlPanel::onPauseClicked()
{
    emit pauseSimulation();
}

void ControlPanel::onResetClicked()
{
    emit resetSimulation();
}

void ControlPanel::onSpeedChanged(int speed)
{
    m_speedLabel->setText(QString::number(speed));
    emit speedChanged(speed);
}
