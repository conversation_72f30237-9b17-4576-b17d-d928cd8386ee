#ifndef CONTROLPANEL_H
#define CONTROLPANEL_H

#include <QGroupBox>
#include <QPushButton>
#include <QSlider>
#include <QLabel>
#include <QHBoxLayout>
#include <QVBoxLayout>

/**
 * @brief Simulation control panel
 * Provides start/pause/reset controls and speed adjustment
 */
class ControlPanel : public QGroupBox
{
    Q_OBJECT

public:
    explicit ControlPanel(QWidget *parent = nullptr);
    ~ControlPanel();

    void setSimulationRunning(bool running);
    int getSpeed() const;

signals:
    void startSimulation();
    void pauseSimulation();
    void resetSimulation();
    void speedChanged(int speed);

private slots:
    void onStartClicked();
    void onPauseClicked();
    void onResetClicked();
    void onSpeedChanged(int speed);

private:
    void setupUI();
    
    // UI components
    QPushButton *m_startButton;
    QPushButton *m_pauseButton;
    QPushButton *m_resetButton;
    QSlider *m_speedSlider;
    QLabel *m_speedLabel;
};

#endif // CONTROLPANEL_H
