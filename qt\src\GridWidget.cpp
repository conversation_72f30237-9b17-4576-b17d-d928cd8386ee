#include "GridWidget.h"
#include <QPaintEvent>
#include <QResizeEvent>
#include <QDebug>
#include <QImage>
#include <cmath>

GridWidget::GridWidget(QWidget *parent)
    : QWidget(parent)
    , m_model(nullptr)
    , m_colorConfig(ColorConfig::instance())
    , m_displayEnabled(true)
    , m_updateFrequency(1)
    , m_updateCounter(0)
    , m_needsRedraw(true)
    , m_cellSize(8)
    , m_updateTimer(new QTimer(this))
{
    setMinimumSize(400, 400);
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    
    // Setup update timer
    m_updateTimer->setSingleShot(true);
    m_updateTimer->setInterval(16); // ~60 FPS max
    connect(m_updateTimer, &QTimer::timeout, this, &GridWidget::updateDisplay);
    
    // Connect to color changes
    connect(m_colorConfig, &ColorConfig::colorsChanged, 
            this, &GridWidget::onColorsChanged);
}

GridWidget::~GridWidget() = default;

void GridWidget::setModel(SchellingModel *model)
{
    if (m_model) {
        disconnect(m_model, nullptr, this, nullptr);
    }
    
    m_model = model;
    
    if (m_model) {
        connect(m_model, &SchellingModel::modelChanged,
                this, &GridWidget::onModelChanged);
    }
    
    markForRedraw();
    update();
}

void GridWidget::setDisplayEnabled(bool enabled)
{
    if (m_displayEnabled != enabled) {
        m_displayEnabled = enabled;
        if (enabled) {
            markForRedraw();
            update();
        }
    }
}

void GridWidget::setUpdateFrequency(int frequency)
{
    m_updateFrequency = qMax(1, frequency);
    m_updateCounter = 0;
}

void GridWidget::markForRedraw()
{
    m_needsRedraw = true;
}

void GridWidget::onModelChanged()
{
    if (!m_displayEnabled) {
        return;
    }
    
    // Check update frequency
    m_updateCounter++;
    if (m_updateCounter < m_updateFrequency) {
        return;
    }
    m_updateCounter = 0;
    
    // Throttle updates using timer
    if (!m_updateTimer->isActive()) {
        m_updateTimer->start();
    }
}

void GridWidget::onColorsChanged()
{
    markForRedraw();
    if (m_displayEnabled) {
        update();
    }
}

void GridWidget::updateDisplay()
{
    markForRedraw();
    update();
}

void GridWidget::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    
    if (!m_model) {
        return;
    }
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing, false);
    
    if (m_needsRedraw || m_cachedImage.isNull()) {
        createGridImage();
    }
    
    if (!m_cachedImage.isNull()) {
        painter.drawPixmap(0, 0, m_cachedImage);
    }
}

void GridWidget::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    markForRedraw();
}

QSize GridWidget::sizeHint() const
{
    if (m_model) {
        int w = m_model->width() * m_cellSize;
        int h = m_model->height() * m_cellSize;
        return QSize(qMin(w, 800), qMin(h, 600));
    }
    return QSize(400, 400);
}

QSize GridWidget::minimumSizeHint() const
{
    return QSize(200, 200);
}

void GridWidget::createGridImage()
{
    if (!m_model) {
        return;
    }
    
    int gridWidth = m_model->width();
    int gridHeight = m_model->height();
    
    if (gridWidth == 0 || gridHeight == 0) {
        return;
    }
    
    // Create image with grid data
    QImage image(gridWidth, gridHeight, QImage::Format_RGB32);
    
    auto colorMapping = m_colorConfig->getColorMapping();
    
    for (int y = 0; y < gridHeight; ++y) {
        for (int x = 0; x < gridWidth; ++x) {
            QColor color = getCellColor(x, y);
            image.setPixelColor(x, y, color);
        }
    }
    
    // Scale to widget size
    QSize widgetSize = size();
    if (widgetSize.isEmpty()) {
        widgetSize = sizeHint();
    }
    
    // Maintain aspect ratio
    double aspectRatio = static_cast<double>(gridWidth) / gridHeight;
    int scaledWidth = widgetSize.width();
    int scaledHeight = static_cast<int>(scaledWidth / aspectRatio);
    
    if (scaledHeight > widgetSize.height()) {
        scaledHeight = widgetSize.height();
        scaledWidth = static_cast<int>(scaledHeight * aspectRatio);
    }
    
    // Scale image
    QImage scaledImage = image.scaled(scaledWidth, scaledHeight, 
                                     Qt::KeepAspectRatio, 
                                     Qt::FastTransformation);
    
    m_cachedImage = QPixmap::fromImage(scaledImage);
    m_needsRedraw = false;
}

QColor GridWidget::getCellColor(int x, int y) const
{
    if (!m_model) {
        return Qt::white;
    }
    
    AgentType cellType = m_model->getCell(x, y);
    bool satisfied = m_model->isSatisfied(x, y);
    
    ColorConfig::ColorType colorType;
    
    switch (cellType) {
    case AgentType::EMPTY:
        colorType = ColorConfig::Empty;
        break;
    case AgentType::TYPE_A:
        colorType = satisfied ? ColorConfig::TypeASatisfied : ColorConfig::TypeAUnsatisfied;
        break;
    case AgentType::TYPE_B:
        colorType = satisfied ? ColorConfig::TypeBSatisfied : ColorConfig::TypeBUnsatisfied;
        break;
    default:
        colorType = ColorConfig::Empty;
        break;
    }
    
    return m_colorConfig->getColor(colorType);
}

void GridWidget::drawGrid(QPainter &painter)
{
    if (!m_model) {
        return;
    }
    
    int gridWidth = m_model->width();
    int gridHeight = m_model->height();
    
    if (gridWidth > 100 || gridHeight > 100) {
        // Don't draw grid lines for large grids
        return;
    }
    
    QSize widgetSize = size();
    double cellWidth = static_cast<double>(widgetSize.width()) / gridWidth;
    double cellHeight = static_cast<double>(widgetSize.height()) / gridHeight;
    
    painter.setPen(QPen(QColor(200, 200, 200), 1));
    
    // Draw vertical lines
    for (int x = 0; x <= gridWidth; ++x) {
        int lineX = static_cast<int>(x * cellWidth);
        painter.drawLine(lineX, 0, lineX, widgetSize.height());
    }
    
    // Draw horizontal lines
    for (int y = 0; y <= gridHeight; ++y) {
        int lineY = static_cast<int>(y * cellHeight);
        painter.drawLine(0, lineY, widgetSize.width(), lineY);
    }
}
