#ifndef GRIDWIDGET_H
#define GRIDWIDGET_H

#include <QWidget>
#include <QPainter>
#include <QPixmap>
#include <QTimer>
#include "SchellingModel.h"
#include "ColorConfig.h"

/**
 * @brief Optimized grid display widget
 * Displays the Schelling model grid with efficient rendering
 */
class GridWidget : public QWidget
{
    Q_OBJECT

public:
    explicit GridWidget(QWidget *parent = nullptr);
    ~GridWidget();

    // Model management
    void setModel(SchellingModel *model);
    SchellingModel *model() const { return m_model; }
    
    // Display control
    void setDisplayEnabled(bool enabled);
    bool isDisplayEnabled() const { return m_displayEnabled; }
    
    void setUpdateFrequency(int frequency);
    int updateFrequency() const { return m_updateFrequency; }
    
    // Force redraw
    void markForRedraw();

public slots:
    void onModelChanged();
    void onColorsChanged();

protected:
    void paintEvent(QPaintEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    QSize sizeHint() const override;
    QSize minimumSizeHint() const override;

private slots:
    void updateDisplay();

private:
    void createGridImage();
    void updateCachedImage();
    QColor getCellColor(int x, int y) const;
    void drawGrid(QPainter &painter);
    
    SchellingModel *m_model;
    ColorConfig *m_colorConfig;
    
    // Display settings
    bool m_displayEnabled;
    int m_updateFrequency;
    int m_updateCounter;
    
    // Rendering optimization
    QPixmap m_cachedImage;
    bool m_needsRedraw;
    int m_cellSize;
    
    // Update timer for throttling
    QTimer *m_updateTimer;
};

#endif // GRIDWIDGET_H
