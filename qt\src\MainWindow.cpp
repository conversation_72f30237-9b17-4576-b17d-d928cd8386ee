#include "MainWindow.h"
#include <QApplication>
#include <QSplitter>
#include <QScrollArea>
#include <QLabel>
#include <QDebug>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_model(nullptr)
    , m_simulationTimer(new QTimer(this))
    , m_simulationRunning(false)
    , m_displayEnabled(true)
    , m_gridWidget(nullptr)
    , m_parameterPanel(nullptr)
    , m_statisticsPanel(nullptr)
    , m_performancePanel(nullptr)
    , m_controlPanel(nullptr)
    , m_colorPanel(nullptr)
    , m_presetCombo(nullptr)
    , m_colorConfig(ColorConfig::instance())
{
    setupUI();
    createModel();
    
    // Setup simulation timer
    connect(m_simulationTimer, &QTimer::timeout, this, &MainWindow::updateSimulation);
    
    // Connect color changes
    connect(m_colorConfig, &ColorConfig::colorsChanged, this, &MainWindow::updateColorButtons);
    
    setWindowTitle("Schelling隔离模型仿真 - C++ Qt版本");
    resize(1400, 900);
    setMinimumSize(1000, 700);
}

MainWindow::~MainWindow()
{
    delete m_model;
}

void MainWindow::setupUI()
{
    auto *centralWidget = new QWidget;
    setCentralWidget(centralWidget);
    
    auto *mainLayout = new QHBoxLayout(centralWidget);
    
    // Create splitter
    auto *splitter = new QSplitter(Qt::Horizontal);
    mainLayout->addWidget(splitter);
    
    // Left panel for controls
    auto *leftPanel = new QWidget;
    leftPanel->setMaximumWidth(350);
    leftPanel->setMinimumWidth(300);
    auto *leftLayout = new QVBoxLayout(leftPanel);
    
    // Parameter panel
    m_parameterPanel = new ParameterPanel;
    connect(m_parameterPanel, &ParameterPanel::parametersChanged,
            this, &MainWindow::onParametersChanged);
    leftLayout->addWidget(m_parameterPanel);
    
    // Control panel
    m_controlPanel = new ControlPanel;
    connect(m_controlPanel, &ControlPanel::startSimulation, this, &MainWindow::startSimulation);
    connect(m_controlPanel, &ControlPanel::pauseSimulation, this, &MainWindow::pauseSimulation);
    connect(m_controlPanel, &ControlPanel::resetSimulation, this, &MainWindow::resetSimulation);
    connect(m_controlPanel, &ControlPanel::speedChanged, this, &MainWindow::onSpeedChanged);
    leftLayout->addWidget(m_controlPanel);
    
    // Statistics panel
    m_statisticsPanel = new StatisticsPanel;
    leftLayout->addWidget(m_statisticsPanel);
    
    // Performance panel
    m_performancePanel = new PerformancePanel;
    connect(m_performancePanel, &PerformancePanel::gpuToggled, this, &MainWindow::onGPUToggled);
    connect(m_performancePanel, &PerformancePanel::displayToggled, this, &MainWindow::onDisplayToggled);
    connect(m_performancePanel, &PerformancePanel::updateFrequencyChanged, 
            this, &MainWindow::onUpdateFrequencyChanged);
    leftLayout->addWidget(m_performancePanel);
    
    // Color configuration panel
    setupColorPanel();
    leftLayout->addWidget(m_colorPanel);
    
    leftLayout->addStretch();
    splitter->addWidget(leftPanel);
    
    // Right panel for grid display
    auto *rightPanel = new QWidget;
    auto *rightLayout = new QVBoxLayout(rightPanel);
    
    // Grid widget in scroll area
    m_gridWidget = new GridWidget;
    auto *scrollArea = new QScrollArea;
    scrollArea->setWidget(m_gridWidget);
    scrollArea->setWidgetResizable(true);
    rightLayout->addWidget(scrollArea);
    
    splitter->addWidget(rightPanel);
    
    // Set splitter proportions
    splitter->setStretchFactor(0, 0);
    splitter->setStretchFactor(1, 1);
}

void MainWindow::setupColorPanel()
{
    m_colorPanel = new QGroupBox("颜色配置");
    auto *layout = new QVBoxLayout(m_colorPanel);
    
    // Preset selection
    auto *presetLayout = new QHBoxLayout;
    presetLayout->addWidget(new QLabel("预设方案:"));
    
    m_presetCombo = new QComboBox;
    auto presetNames = m_colorConfig->getPresetNames();
    for (const QString &preset : presetNames) {
        QString displayName = m_colorConfig->getPresetDisplayName(preset);
        m_presetCombo->addItem(displayName, preset);
    }
    connect(m_presetCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &MainWindow::onPresetChanged);
    presetLayout->addWidget(m_presetCombo);
    
    layout->addLayout(presetLayout);
    
    // Color buttons
    auto *colorsLayout = new QGridLayout;
    
    QList<ColorConfig::ColorType> colorTypes = {
        ColorConfig::Empty,
        ColorConfig::TypeASatisfied,
        ColorConfig::TypeAUnsatisfied,
        ColorConfig::TypeBSatisfied,
        ColorConfig::TypeBUnsatisfied
    };
    
    int row = 0;
    for (auto colorType : colorTypes) {
        QString name = m_colorConfig->getColorTypeName(colorType);
        auto *label = new QLabel(name + ":");
        auto *button = new QPushButton;
        button->setFixedSize(40, 25);
        
        // Store color type in button property
        button->setProperty("colorType", static_cast<int>(colorType));
        connect(button, &QPushButton::clicked, this, &MainWindow::onColorButtonClicked);
        
        colorsLayout->addWidget(label, row, 0);
        colorsLayout->addWidget(button, row, 1);
        
        m_colorButtons[colorType] = button;
        row++;
    }
    
    layout->addLayout(colorsLayout);
    
    // Reset button
    auto *resetBtn = new QPushButton("重置为默认");
    connect(resetBtn, &QPushButton::clicked, this, &MainWindow::onResetColorsClicked);
    layout->addWidget(resetBtn);
    
    updateColorButtons();
}

void MainWindow::createModel()
{
    delete m_model;
    
    m_model = new SchellingModel(this);
    
    // Get parameters from panel
    auto params = m_parameterPanel->getParameters();
    m_model->initialize(params);
    
    // Connect model to UI
    m_gridWidget->setModel(m_model);
    connect(m_model, &SchellingModel::statisticsChanged,
            m_statisticsPanel, &StatisticsPanel::updateStatistics);
    connect(m_model, &SchellingModel::statisticsChanged,
            m_performancePanel, &PerformancePanel::updatePerformance);
    
    qDebug() << "Model created with size:" << params.width << "x" << params.height;
}

void MainWindow::updateColorButtons()
{
    for (auto it = m_colorButtons.begin(); it != m_colorButtons.end(); ++it) {
        QColor color = m_colorConfig->getColor(it.key());
        QString styleSheet = QString("background-color: rgb(%1, %2, %3)")
                           .arg(color.red()).arg(color.green()).arg(color.blue());
        it.value()->setStyleSheet(styleSheet);
    }
}

void MainWindow::startSimulation()
{
    m_simulationRunning = true;
    m_controlPanel->setSimulationRunning(true);
    
    // Calculate timer interval based on speed (1-20 -> 500ms-25ms)
    int speed = m_controlPanel->getSpeed();
    int interval = qMax(25, 525 - speed * 25);
    m_simulationTimer->start(interval);
}

void MainWindow::pauseSimulation()
{
    m_simulationRunning = false;
    m_controlPanel->setSimulationRunning(false);
    m_simulationTimer->stop();
}

void MainWindow::resetSimulation()
{
    pauseSimulation();
    if (m_model) {
        m_model->reset();
    }
}

void MainWindow::updateSimulation()
{
    if (m_model && m_simulationRunning) {
        if (!m_model->step()) {
            // Simulation finished
            pauseSimulation();
        }
    }
}

void MainWindow::onParametersChanged()
{
    if (m_simulationRunning) {
        pauseSimulation();
    }
    createModel();
}

void MainWindow::onSpeedChanged(int speed)
{
    if (m_simulationTimer->isActive()) {
        int interval = qMax(25, 525 - speed * 25);
        m_simulationTimer->setInterval(interval);
    }
}

void MainWindow::onGPUToggled(bool enabled)
{
    Q_UNUSED(enabled)
    // GPU switching would be implemented here
    // For now, this is just a placeholder
    qDebug() << "GPU toggled:" << enabled;
}

void MainWindow::onDisplayToggled(bool enabled)
{
    m_displayEnabled = enabled;
    if (m_gridWidget) {
        m_gridWidget->setDisplayEnabled(enabled);
    }
}

void MainWindow::onUpdateFrequencyChanged(int frequency)
{
    if (m_gridWidget) {
        m_gridWidget->setUpdateFrequency(frequency);
    }
}

void MainWindow::onPresetChanged()
{
    QString presetKey = m_presetCombo->currentData().toString();
    if (!presetKey.isEmpty()) {
        m_colorConfig->loadPreset(presetKey);
    }
}

void MainWindow::onColorButtonClicked()
{
    auto *button = qobject_cast<QPushButton*>(sender());
    if (button) {
        int colorTypeInt = button->property("colorType").toInt();
        auto colorType = static_cast<ColorConfig::ColorType>(colorTypeInt);
        chooseColor(colorType);
    }
}

void MainWindow::chooseColor(ColorConfig::ColorType colorType)
{
    QColor currentColor = m_colorConfig->getColor(colorType);
    QString typeName = m_colorConfig->getColorTypeName(colorType);
    
    QColor newColor = QColorDialog::getColor(currentColor, this, 
                                           QString("选择%1颜色").arg(typeName));
    
    if (newColor.isValid()) {
        m_colorConfig->setColor(colorType, newColor);
    }
}

void MainWindow::onResetColorsClicked()
{
    m_colorConfig->resetToDefault();
}
