#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTimer>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QSplitter>
#include <QScrollArea>
#include <QGroupBox>
#include <QComboBox>
#include <QPushButton>
#include <QColorDialog>
#include <QGridLayout>

#include "SchellingModel.h"
#include "GridWidget.h"
#include "ParameterPanel.h"
#include "StatisticsPanel.h"
#include "PerformancePanel.h"
#include "ControlPanel.h"
#include "ColorConfig.h"

/**
 * @brief Main application window
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    // Simulation control
    void startSimulation();
    void pauseSimulation();
    void resetSimulation();
    void updateSimulation();
    
    // Parameter changes
    void onParametersChanged();
    void onSpeedChanged(int speed);
    
    // Performance control
    void onGPUToggled(bool enabled);
    void onDisplayToggled(bool enabled);
    void onUpdateFrequencyChanged(int frequency);
    
    // Color configuration
    void onPresetChanged();
    void onColorButtonClicked();
    void onResetColorsClicked();

private:
    void setupUI();
    void setupColorPanel();
    void createModel();
    void updateColorButtons();
    void chooseColor(ColorConfig::ColorType colorType);
    
    // Core components
    SchellingModel *m_model;
    QTimer *m_simulationTimer;
    bool m_simulationRunning;
    bool m_displayEnabled;
    
    // UI components
    GridWidget *m_gridWidget;
    ParameterPanel *m_parameterPanel;
    StatisticsPanel *m_statisticsPanel;
    PerformancePanel *m_performancePanel;
    ControlPanel *m_controlPanel;
    
    // Color configuration panel
    QGroupBox *m_colorPanel;
    QComboBox *m_presetCombo;
    QMap<ColorConfig::ColorType, QPushButton*> m_colorButtons;
    
    ColorConfig *m_colorConfig;
};

#endif // MAINWINDOW_H
