#include "ParameterPanel.h"
#include <QLabel>
#include <QLocale>

ParameterPanel::ParameterPanel(QWidget *parent)
    : QGroupBox("参数配置", parent)
    , m_widthSpinBox(nullptr)
    , m_heightSpinBox(nullptr)
    , m_densitySpinBox(nullptr)
    , m_minoritySpinBox(nullptr)
    , m_homophilySpinBox(nullptr)
    , m_sizeWarningLabel(nullptr)
{
    setupUI();
}

ParameterPanel::~ParameterPanel() = default;

void ParameterPanel::setupUI()
{
    auto *layout = new QGridLayout(this);
    
    // Grid size
    layout->addWidget(new QLabel("网格宽度:"), 0, 0);
    m_widthSpinBox = new QSpinBox;
    m_widthSpinBox->setRange(10, 100000);
    m_widthSpinBox->setValue(100);
    connect(m_widthSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &ParameterPanel::onParameterChanged);
    connect(m_widthSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &ParameterPanel::checkGridSize);
    layout->addWidget(m_widthSpinBox, 0, 1);
    
    layout->addWidget(new QLabel("网格高度:"), 1, 0);
    m_heightSpinBox = new QSpinBox;
    m_heightSpinBox->setRange(10, 100000);
    m_heightSpinBox->setValue(100);
    connect(m_heightSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &ParameterPanel::onParameterChanged);
    connect(m_heightSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &ParameterPanel::checkGridSize);
    layout->addWidget(m_heightSpinBox, 1, 1);
    
    // Population density
    layout->addWidget(new QLabel("人口密度:"), 2, 0);
    m_densitySpinBox = new QDoubleSpinBox;
    m_densitySpinBox->setRange(0.1, 1.0);
    m_densitySpinBox->setSingleStep(0.1);
    m_densitySpinBox->setValue(0.8);
    m_densitySpinBox->setDecimals(2);
    connect(m_densitySpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &ParameterPanel::onParameterChanged);
    layout->addWidget(m_densitySpinBox, 2, 1);
    
    // Minority ratio
    layout->addWidget(new QLabel("少数群体比例:"), 3, 0);
    m_minoritySpinBox = new QDoubleSpinBox;
    m_minoritySpinBox->setRange(0.1, 0.9);
    m_minoritySpinBox->setSingleStep(0.1);
    m_minoritySpinBox->setValue(0.3);
    m_minoritySpinBox->setDecimals(2);
    connect(m_minoritySpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &ParameterPanel::onParameterChanged);
    layout->addWidget(m_minoritySpinBox, 3, 1);
    
    // Homophily threshold
    layout->addWidget(new QLabel("相似性阈值:"), 4, 0);
    m_homophilySpinBox = new QDoubleSpinBox;
    m_homophilySpinBox->setRange(0.0, 1.0);
    m_homophilySpinBox->setSingleStep(0.1);
    m_homophilySpinBox->setValue(0.3);
    m_homophilySpinBox->setDecimals(2);
    connect(m_homophilySpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &ParameterPanel::onParameterChanged);
    layout->addWidget(m_homophilySpinBox, 4, 1);
    
    // Size warning label
    m_sizeWarningLabel = new QLabel;
    m_sizeWarningLabel->setWordWrap(true);
    m_sizeWarningLabel->setStyleSheet("color: #e74c3c; font-size: 10px;");
    layout->addWidget(m_sizeWarningLabel, 5, 0, 1, 2);
    
    // Initial check
    checkGridSize();
}

ModelParameters ParameterPanel::getParameters() const
{
    ModelParameters params;
    params.width = m_widthSpinBox->value();
    params.height = m_heightSpinBox->value();
    params.density = m_densitySpinBox->value();
    params.minorityRatio = m_minoritySpinBox->value();
    params.homophily = m_homophilySpinBox->value();
    params.useGPU = true; // Default to GPU if available
    return params;
}

void ParameterPanel::setParameters(const ModelParameters &params)
{
    // Block signals to avoid triggering parameter changes
    m_widthSpinBox->blockSignals(true);
    m_heightSpinBox->blockSignals(true);
    m_densitySpinBox->blockSignals(true);
    m_minoritySpinBox->blockSignals(true);
    m_homophilySpinBox->blockSignals(true);
    
    m_widthSpinBox->setValue(params.width);
    m_heightSpinBox->setValue(params.height);
    m_densitySpinBox->setValue(params.density);
    m_minoritySpinBox->setValue(params.minorityRatio);
    m_homophilySpinBox->setValue(params.homophily);
    
    // Unblock signals
    m_widthSpinBox->blockSignals(false);
    m_heightSpinBox->blockSignals(false);
    m_densitySpinBox->blockSignals(false);
    m_minoritySpinBox->blockSignals(false);
    m_homophilySpinBox->blockSignals(false);
    
    checkGridSize();
}

void ParameterPanel::onParameterChanged()
{
    emit parametersChanged();
}

void ParameterPanel::checkGridSize()
{
    updateSizeWarning();
}

void ParameterPanel::updateSizeWarning()
{
    int width = m_widthSpinBox->value();
    int height = m_heightSpinBox->value();
    long long totalCells = static_cast<long long>(width) * height;
    
    // Estimate memory usage (int32 + bool + temporary arrays)
    double memoryMB = totalCells * 13.0 / (1024.0 * 1024.0);
    
    QLocale locale;
    QString cellsStr = locale.toString(totalCells);
    
    if (totalCells > 10000000LL) { // 10 million cells
        m_sizeWarningLabel->setText(
            QString("⚠️ 超大网格 (%1×%2=%3个单元格)\n"
                   "预估内存: ~%4MB\n"
                   "建议：关闭界面显示，使用GPU加速")
            .arg(width).arg(height).arg(cellsStr).arg(static_cast<int>(memoryMB))
        );
        m_sizeWarningLabel->setStyleSheet("color: #e74c3c; font-size: 10px; font-weight: bold;");
    } else if (totalCells > 1000000LL) { // 1 million cells
        m_sizeWarningLabel->setText(
            QString("⚡ 大网格 (%1×%2=%3个单元格)\n"
                   "预估内存: ~%4MB\n"
                   "建议：关闭界面显示以提高性能")
            .arg(width).arg(height).arg(cellsStr).arg(static_cast<int>(memoryMB))
        );
        m_sizeWarningLabel->setStyleSheet("color: #f39c12; font-size: 10px;");
    } else if (totalCells > 100000LL) { // 100k cells
        m_sizeWarningLabel->setText(
            QString("💡 中等网格 (%1×%2=%3个单元格)\n"
                   "预估内存: ~%4MB\n"
                   "可考虑降低界面更新频率")
            .arg(width).arg(height).arg(cellsStr).arg(memoryMB, 0, 'f', 1)
        );
        m_sizeWarningLabel->setStyleSheet("color: #3498db; font-size: 10px;");
    } else {
        m_sizeWarningLabel->setText("");
    }
}
