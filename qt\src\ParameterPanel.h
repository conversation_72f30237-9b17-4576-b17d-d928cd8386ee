#ifndef PARAMETERPANEL_H
#define PARAMETERPANEL_H

#include <QGroupBox>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QLabel>
#include <QGridLayout>
#include <QVBoxLayout>
#include "AgentType.h"

/**
 * @brief Parameter configuration panel
 * Allows users to configure model parameters
 */
class ParameterPanel : public QGroupBox
{
    Q_OBJECT

public:
    explicit ParameterPanel(QWidget *parent = nullptr);
    ~ParameterPanel();

    // Parameter access
    ModelParameters getParameters() const;
    void setParameters(const ModelParameters &params);

signals:
    void parametersChanged();

private slots:
    void onParameterChanged();
    void checkGridSize();

private:
    void setupUI();
    void updateSizeWarning();
    
    // UI components
    QSpinBox *m_widthSpinBox;
    QSpinBox *m_heightSpinBox;
    QDoubleSpinBox *m_densitySpinBox;
    QDoubleSpinBox *m_minoritySpinBox;
    QDoubleSpinBox *m_homophilySpinBox;
    
    QLabel *m_sizeWarningLabel;
};

#endif // PARAMETERPANEL_H
