#include "PerformancePanel.h"
#include <QLabel>
#include <QWidget>

PerformancePanel::PerformancePanel(QWidget *parent)
    : QGroupBox("性能监控", parent)
{
    setupUI();
}

PerformancePanel::~PerformancePanel() = default;

void PerformancePanel::setupUI()
{
    auto *layout = new QGridLayout(this);
    
    // Performance metrics
    m_fpsLabel = new QLabel("FPS: 0.0");
    layout->addWidget(m_fpsLabel, 0, 0);
    
    m_stepTimeLabel = new QLabel("步骤时间: 0.000s");
    layout->addWidget(m_stepTimeLabel, 1, 0);
    
    m_backendLabel = new QLabel("计算后端: CPU");
    layout->addWidget(m_backendLabel, 2, 0);
    
    m_gpuStatusLabel = new QLabel("GPU状态: 检测中...");
    layout->addWidget(m_gpuStatusLabel, 3, 0);
    
    // GPU control
    m_gpuCheckBox = new QCheckBox("使用GPU加速");
    m_gpuCheckBox->setChecked(true);
    connect(m_gpuCheckBox, &QCheckBox::toggled, this, &PerformancePanel::onGPUToggled);
    layout->addWidget(m_gpuCheckBox, 4, 0);
    
    // Display control
    m_displayCheckBox = new QCheckBox("实时显示界面");
    m_displayCheckBox->setChecked(true);
    m_displayCheckBox->setToolTip("关闭可显著提高仿真速度");
    connect(m_displayCheckBox, &QCheckBox::toggled, this, &PerformancePanel::onDisplayToggled);
    layout->addWidget(m_displayCheckBox, 5, 0);
    
    // Update frequency control
    auto *freqWidget = new QWidget;
    auto *freqLayout = new QHBoxLayout(freqWidget);
    freqLayout->setContentsMargins(0, 0, 0, 0);
    
    freqLayout->addWidget(new QLabel("更新频率:"));
    m_updateFreqSlider = new QSlider(Qt::Horizontal);
    m_updateFreqSlider->setRange(1, 10);
    m_updateFreqSlider->setValue(1);
    m_updateFreqSlider->setToolTip("每N步更新一次界面");
    connect(m_updateFreqSlider, &QSlider::valueChanged, this, &PerformancePanel::onUpdateFrequencyChanged);
    freqLayout->addWidget(m_updateFreqSlider);
    
    m_freqLabel = new QLabel("1");
    freqLayout->addWidget(m_freqLabel);
    
    layout->addWidget(freqWidget, 6, 0);
    
    // Performance hint
    m_perfHintLabel = new QLabel("💡 关闭界面显示可提升10-50倍速度");
    m_perfHintLabel->setStyleSheet("color: #7f8c8d; font-size: 10px;");
    m_perfHintLabel->setWordWrap(true);
    layout->addWidget(m_perfHintLabel, 7, 0);
}

bool PerformancePanel::isGPUEnabled() const
{
    return m_gpuCheckBox->isChecked();
}

bool PerformancePanel::isDisplayEnabled() const
{
    return m_displayCheckBox->isChecked();
}

int PerformancePanel::updateFrequency() const
{
    return m_updateFreqSlider->value();
}

void PerformancePanel::updatePerformance(const ModelStatistics &stats)
{
    m_fpsLabel->setText(QString("FPS: %1").arg(stats.fps, 0, 'f', 1));
    m_stepTimeLabel->setText(QString("步骤时间: %1s").arg(stats.lastStepTime, 0, 'f', 3));
    
    QString backend = stats.usingGPU ? "GPU" : "CPU";
    m_backendLabel->setText(QString("计算后端: %1").arg(backend));
    
    // Update GPU status
    if (stats.usingGPU) {
        m_gpuStatusLabel->setText("GPU状态: 活跃");
        m_gpuStatusLabel->setStyleSheet("color: green");
    } else {
        m_gpuStatusLabel->setText("GPU状态: 未使用");
        m_gpuStatusLabel->setStyleSheet("color: orange");
    }
    
    updatePerformanceHint();
}

void PerformancePanel::onGPUToggled(bool enabled)
{
    emit gpuToggled(enabled);
}

void PerformancePanel::onDisplayToggled(bool enabled)
{
    emit displayToggled(enabled);
    updatePerformanceHint();
}

void PerformancePanel::onUpdateFrequencyChanged(int frequency)
{
    m_freqLabel->setText(QString::number(frequency));
    emit updateFrequencyChanged(frequency);
    updatePerformanceHint();
}

void PerformancePanel::updatePerformanceHint()
{
    bool displayEnabled = m_displayCheckBox->isChecked();
    int updateFreq = m_updateFreqSlider->value();
    
    if (!displayEnabled) {
        m_perfHintLabel->setText("🚀 高速模式：界面已关闭");
        m_perfHintLabel->setStyleSheet("color: #27ae60; font-size: 10px; font-weight: bold;");
    } else if (updateFreq > 1) {
        m_perfHintLabel->setText(QString("⚡ 优化模式：每%1步更新界面").arg(updateFreq));
        m_perfHintLabel->setStyleSheet("color: #f39c12; font-size: 10px;");
    } else {
        m_perfHintLabel->setText("💡 关闭界面显示可提升10-50倍速度");
        m_perfHintLabel->setStyleSheet("color: #7f8c8d; font-size: 10px;");
    }
}
