#ifndef PERFORMANCEPANEL_H
#define PERFORMANCEPANEL_H

#include <QGroupBox>
#include <QLabel>
#include <QCheckBox>
#include <QSlider>
#include <QGridLayout>
#include <QHBoxLayout>
#include "AgentType.h"

/**
 * @brief Performance monitoring and control panel
 */
class PerformancePanel : public QGroupBox
{
    Q_OBJECT

public:
    explicit PerformancePanel(QWidget *parent = nullptr);
    ~PerformancePanel();

    // Control access
    bool isGPUEnabled() const;
    bool isDisplayEnabled() const;
    int updateFrequency() const;

signals:
    void gpuToggled(bool enabled);
    void displayToggled(bool enabled);
    void updateFrequencyChanged(int frequency);

public slots:
    void updatePerformance(const ModelStatistics &stats);

private slots:
    void onGPUToggled(bool enabled);
    void onDisplayToggled(bool enabled);
    void onUpdateFrequencyChanged(int frequency);

private:
    void setupUI();
    void updatePerformanceHint();
    
    // UI components
    QLabel *m_fpsLabel;
    QLabel *m_stepTimeLabel;
    QLabel *m_backendLabel;
    QLabel *m_gpuStatusLabel;
    
    QCheckBox *m_gpuCheckBox;
    QCheckBox *m_displayCheckBox;
    QSlider *m_updateFreqSlider;
    QLabel *m_freqLabel;
    QLabel *m_perfHintLabel;
};

#endif // PERFORMANCEPANEL_H
