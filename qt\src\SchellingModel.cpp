#include "SchellingModel.h"
#include <QDebug>
#include <QRandomGenerator>
#include <algorithm>
#include <cmath>

SchellingModel::SchellingModel(QObject *parent)
    : QObject(parent)
    , m_iteration(0)
    , m_totalAgents(0)
    , m_satisfiedAgents(0)
    , m_segregationIndex(0.0)
    , m_lastStepTime(0.0)
    , m_avgStepTime(0.0)
    , m_rng(QRandomGenerator::global()->generate())
    , m_uniform(0.0, 1.0)
{
    // Initialize with default parameters
    ModelParameters defaultParams;
    setParameters(defaultParams);
}

SchellingModel::~SchellingModel() = default;

void SchellingModel::initialize(const ModelParameters &params)
{
    setParameters(params);
    reset();
}

void SchellingModel::reset()
{
    // Initialize grid
    m_grid = QVector<QVector<AgentType>>(m_params.height, 
                                        QVector<AgentType>(m_params.width, AgentType::EMPTY));
    m_satisfaction = QVector<QVector<bool>>(m_params.height, 
                                           QVector<bool>(m_params.width, false));
    
    // Reset statistics
    m_iteration = 0;
    m_totalAgents = 0;
    m_satisfiedAgents = 0;
    m_segregationIndex = 0.0;
    m_lastStepTime = 0.0;
    m_avgStepTime = 0.0;
    
    // Initialize population
    initializePopulation();
    updateSatisfaction();
    updateStatistics();
    
    emit modelChanged();
}

void SchellingModel::setParameters(const ModelParameters &params)
{
    m_params = params;
}

bool SchellingModel::step()
{
    m_stepTimer.start();
    
    // Get unsatisfied agents and empty cells
    auto unsatisfiedAgents = getUnsatisfiedAgents();
    auto emptyCells = getEmptyCells();
    
    if (unsatisfiedAgents.isEmpty() || emptyCells.isEmpty()) {
        return false; // Simulation finished
    }
    
    // Randomly select an unsatisfied agent and an empty cell
    int agentIndex = QRandomGenerator::global()->bounded(unsatisfiedAgents.size());
    int emptyIndex = QRandomGenerator::global()->bounded(emptyCells.size());
    
    auto agentPos = unsatisfiedAgents[agentIndex];
    auto emptyPos = emptyCells[emptyIndex];
    
    // Move agent
    AgentType agentType = m_grid[agentPos.second][agentPos.first];
    m_grid[emptyPos.second][emptyPos.first] = agentType;
    m_grid[agentPos.second][agentPos.first] = AgentType::EMPTY;
    
    // Update satisfaction
    updateSatisfaction();
    
    // Update statistics
    m_iteration++;
    m_lastStepTime = m_stepTimer.elapsed() / 1000.0;
    if (m_avgStepTime == 0.0) {
        m_avgStepTime = m_lastStepTime;
    } else {
        m_avgStepTime = 0.9 * m_avgStepTime + 0.1 * m_lastStepTime;
    }
    
    updateStatistics();
    emit modelChanged();
    
    return true;
}

AgentType SchellingModel::getCell(int x, int y) const
{
    if (isValidPosition(x, y)) {
        return m_grid[y][x];
    }
    return AgentType::EMPTY;
}

bool SchellingModel::isSatisfied(int x, int y) const
{
    if (isValidPosition(x, y)) {
        return m_satisfaction[y][x];
    }
    return false;
}

void SchellingModel::initializePopulation()
{
    // Calculate population
    int totalCells = m_params.width * m_params.height;
    m_totalAgents = static_cast<int>(totalCells * m_params.density);
    int minorityCount = static_cast<int>(m_totalAgents * m_params.minorityRatio);
    int majorityCount = m_totalAgents - minorityCount;
    
    // Create list of all positions
    QVector<QPair<int, int>> positions;
    for (int y = 0; y < m_params.height; ++y) {
        for (int x = 0; x < m_params.width; ++x) {
            positions.append({x, y});
        }
    }
    
    // Shuffle positions
    std::shuffle(positions.begin(), positions.end(), m_rng);
    
    // Place agents
    int posIndex = 0;
    
    // Place minority agents (TYPE_A)
    for (int i = 0; i < minorityCount && posIndex < positions.size(); ++i, ++posIndex) {
        auto pos = positions[posIndex];
        m_grid[pos.second][pos.first] = AgentType::TYPE_A;
    }
    
    // Place majority agents (TYPE_B)
    for (int i = 0; i < majorityCount && posIndex < positions.size(); ++i, ++posIndex) {
        auto pos = positions[posIndex];
        m_grid[pos.second][pos.first] = AgentType::TYPE_B;
    }
    
    qDebug() << "Initialized population:" << minorityCount << "TYPE_A," << majorityCount << "TYPE_B";
}

void SchellingModel::updateSatisfaction()
{
    m_satisfiedAgents = 0;
    
    for (int y = 0; y < m_params.height; ++y) {
        for (int x = 0; x < m_params.width; ++x) {
            if (m_grid[y][x] == AgentType::EMPTY) {
                m_satisfaction[y][x] = false;
                continue;
            }
            
            int sameNeighbors = countSameNeighbors(x, y);
            int totalNeighbors = countTotalNeighbors(x, y);
            
            bool satisfied = false;
            if (totalNeighbors == 0) {
                satisfied = true; // Agents with no neighbors are satisfied
            } else {
                double ratio = static_cast<double>(sameNeighbors) / totalNeighbors;
                satisfied = ratio >= m_params.homophily;
            }
            
            m_satisfaction[y][x] = satisfied;
            if (satisfied) {
                m_satisfiedAgents++;
            }
        }
    }
    
    calculateSegregationIndex();
}

void SchellingModel::calculateSegregationIndex()
{
    if (m_totalAgents == 0) {
        m_segregationIndex = 0.0;
        return;
    }
    
    int typeACount = 0, typeBCount = 0;
    double typeASum = 0.0, typeBSum = 0.0;
    
    for (int y = 0; y < m_params.height; ++y) {
        for (int x = 0; x < m_params.width; ++x) {
            AgentType type = m_grid[y][x];
            if (type == AgentType::EMPTY) continue;
            
            int sameNeighbors = countSameNeighbors(x, y);
            int totalNeighbors = countTotalNeighbors(x, y);
            
            if (totalNeighbors > 0) {
                double ratio = static_cast<double>(sameNeighbors) / totalNeighbors;
                
                if (type == AgentType::TYPE_A) {
                    typeASum += ratio;
                    typeACount++;
                } else if (type == AgentType::TYPE_B) {
                    typeBSum += ratio;
                    typeBCount++;
                }
            }
        }
    }
    
    double avgRatioA = typeACount > 0 ? typeASum / typeACount : 0.0;
    double avgRatioB = typeBCount > 0 ? typeBSum / typeBCount : 0.0;
    
    m_segregationIndex = (avgRatioA + avgRatioB) / 2.0;
}

QVector<QPair<int, int>> SchellingModel::getNeighbors(int x, int y) const
{
    QVector<QPair<int, int>> neighbors;
    
    for (int dy = -1; dy <= 1; ++dy) {
        for (int dx = -1; dx <= 1; ++dx) {
            if (dx == 0 && dy == 0) continue;
            
            int nx = x + dx;
            int ny = y + dy;
            
            if (isValidPosition(nx, ny)) {
                neighbors.append({nx, ny});
            }
        }
    }
    
    return neighbors;
}

int SchellingModel::countSameNeighbors(int x, int y) const
{
    if (!isValidPosition(x, y) || m_grid[y][x] == AgentType::EMPTY) {
        return 0;
    }
    
    AgentType agentType = m_grid[y][x];
    int count = 0;
    
    auto neighbors = getNeighbors(x, y);
    for (const auto &neighbor : neighbors) {
        if (m_grid[neighbor.second][neighbor.first] == agentType) {
            count++;
        }
    }
    
    return count;
}

int SchellingModel::countTotalNeighbors(int x, int y) const
{
    if (!isValidPosition(x, y) || m_grid[y][x] == AgentType::EMPTY) {
        return 0;
    }
    
    int count = 0;
    auto neighbors = getNeighbors(x, y);
    for (const auto &neighbor : neighbors) {
        if (m_grid[neighbor.second][neighbor.first] != AgentType::EMPTY) {
            count++;
        }
    }
    
    return count;
}

QVector<QPair<int, int>> SchellingModel::getEmptyCells() const
{
    QVector<QPair<int, int>> emptyCells;
    
    for (int y = 0; y < m_params.height; ++y) {
        for (int x = 0; x < m_params.width; ++x) {
            if (m_grid[y][x] == AgentType::EMPTY) {
                emptyCells.append({x, y});
            }
        }
    }
    
    return emptyCells;
}

QVector<QPair<int, int>> SchellingModel::getUnsatisfiedAgents() const
{
    QVector<QPair<int, int>> unsatisfied;
    
    for (int y = 0; y < m_params.height; ++y) {
        for (int x = 0; x < m_params.width; ++x) {
            if (m_grid[y][x] != AgentType::EMPTY && !m_satisfaction[y][x]) {
                unsatisfied.append({x, y});
            }
        }
    }
    
    return unsatisfied;
}

ModelStatistics SchellingModel::getStatistics() const
{
    ModelStatistics stats;
    
    stats.iteration = m_iteration;
    stats.totalAgents = m_totalAgents;
    stats.satisfiedAgents = m_satisfiedAgents;
    stats.satisfactionRate = m_totalAgents > 0 ? 
        static_cast<double>(m_satisfiedAgents) / m_totalAgents : 0.0;
    stats.segregationIndex = m_segregationIndex;
    stats.emptyCells = m_params.width * m_params.height - m_totalAgents;
    
    // Count agent types
    stats.typeACount = 0;
    stats.typeBCount = 0;
    int typeASatisfied = 0;
    int typeBSatisfied = 0;
    
    for (int y = 0; y < m_params.height; ++y) {
        for (int x = 0; x < m_params.width; ++x) {
            AgentType type = m_grid[y][x];
            bool satisfied = m_satisfaction[y][x];
            
            if (type == AgentType::TYPE_A) {
                stats.typeACount++;
                if (satisfied) typeASatisfied++;
            } else if (type == AgentType::TYPE_B) {
                stats.typeBCount++;
                if (satisfied) typeBSatisfied++;
            }
        }
    }
    
    stats.typeASatisfaction = stats.typeACount > 0 ? 
        static_cast<double>(typeASatisfied) / stats.typeACount : 0.0;
    stats.typeBSatisfaction = stats.typeBCount > 0 ? 
        static_cast<double>(typeBSatisfied) / stats.typeBCount : 0.0;
    
    stats.densityActual = static_cast<double>(m_totalAgents) / 
        (m_params.width * m_params.height);
    stats.minorityRatioActual = m_totalAgents > 0 ? 
        static_cast<double>(stats.typeACount) / m_totalAgents : 0.0;
    
    stats.lastStepTime = m_lastStepTime;
    stats.avgStepTime = m_avgStepTime;
    stats.usingGPU = false; // CPU version
    stats.fps = m_avgStepTime > 0 ? 1.0 / m_avgStepTime : 0.0;
    
    return stats;
}

void SchellingModel::updateStatistics()
{
    auto stats = getStatistics();
    emit statisticsChanged(stats);
}

bool SchellingModel::isValidPosition(int x, int y) const
{
    return x >= 0 && x < m_params.width && y >= 0 && y < m_params.height;
}
