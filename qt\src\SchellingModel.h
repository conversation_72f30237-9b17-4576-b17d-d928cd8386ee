#ifndef SCHELLINGMODEL_H
#define SCHELLINGMODEL_H

#include <QObject>
#include <QVector>
#include <QTimer>
#include <QElapsedTimer>
#include <random>
#include "AgentType.h"

/**
 * @brief CPU-based Schelling segregation model
 */
class SchellingModel : public QObject
{
    Q_OBJECT

public:
    explicit SchellingModel(QObject *parent = nullptr);
    virtual ~SchellingModel();

    // Model setup
    void initialize(const ModelParameters &params);
    void reset();
    
    // Simulation control
    virtual bool step();
    
    // Data access
    const QVector<QVector<AgentType>>& getGrid() const { return m_grid; }
    const QVector<QVector<bool>>& getSatisfaction() const { return m_satisfaction; }
    ModelStatistics getStatistics() const;
    
    // Parameters
    void setParameters(const ModelParameters &params);
    ModelParameters getParameters() const { return m_params; }
    
    // Grid access
    int width() const { return m_params.width; }
    int height() const { return m_params.height; }
    AgentType getCell(int x, int y) const;
    bool isSatisfied(int x, int y) const;

signals:
    void modelChanged();
    void statisticsChanged(const ModelStatistics &stats);

protected:
    // Core algorithms
    virtual void initializePopulation();
    virtual void updateSatisfaction();
    virtual void calculateSegregationIndex();
    
    // Helper methods
    QVector<QPair<int, int>> getNeighbors(int x, int y) const;
    int countSameNeighbors(int x, int y) const;
    int countTotalNeighbors(int x, int y) const;
    QVector<QPair<int, int>> getEmptyCells() const;
    QVector<QPair<int, int>> getUnsatisfiedAgents() const;
    
    // Data members
    ModelParameters m_params;
    QVector<QVector<AgentType>> m_grid;
    QVector<QVector<bool>> m_satisfaction;
    
    // Statistics
    int m_iteration;
    int m_totalAgents;
    int m_satisfiedAgents;
    double m_segregationIndex;
    
    // Performance tracking
    QElapsedTimer m_stepTimer;
    double m_lastStepTime;
    double m_avgStepTime;
    
    // Random number generation
    std::mt19937 m_rng;
    std::uniform_real_distribution<double> m_uniform;
    
private:
    void updateStatistics();
    bool isValidPosition(int x, int y) const;
};

#endif // SCHELLINGMODEL_H
