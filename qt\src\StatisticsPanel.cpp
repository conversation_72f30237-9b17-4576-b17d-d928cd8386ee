#include "StatisticsPanel.h"
#include <QLabel>

StatisticsPanel::StatisticsPanel(QWidget *parent)
    : QGroupBox("统计信息", parent)
{
    setupUI();
}

StatisticsPanel::~StatisticsPanel() = default;

void StatisticsPanel::setupUI()
{
    auto *layout = new QGridLayout(this);
    
    // Basic statistics
    layout->addWidget(new QLabel("迭代次数:"), 0, 0);
    m_iterationLabel = new QLabel("0");
    layout->addWidget(m_iterationLabel, 0, 1);
    
    layout->addWidget(new QLabel("总体满意度:"), 1, 0);
    m_satisfactionLabel = new QLabel("0.00%");
    layout->addWidget(m_satisfactionLabel, 1, 1);
    
    layout->addWidget(new QLabel("隔离指数:"), 2, 0);
    m_segregationLabel = new QLabel("0.000");
    layout->addWidget(m_segregationLabel, 2, 1);
    
    layout->addWidget(new QLabel("代理总数:"), 3, 0);
    m_agentsLabel = new QLabel("0");
    layout->addWidget(m_agentsLabel, 3, 1);
    
    // Group statistics
    layout->addWidget(new QLabel("少数群体:"), 4, 0);
    m_typeACountLabel = new QLabel("0");
    layout->addWidget(m_typeACountLabel, 4, 1);
    
    layout->addWidget(new QLabel("满意度:"), 5, 0);
    m_typeASatisfactionLabel = new QLabel("0.00%");
    layout->addWidget(m_typeASatisfactionLabel, 5, 1);
    
    layout->addWidget(new QLabel("多数群体:"), 6, 0);
    m_typeBCountLabel = new QLabel("0");
    layout->addWidget(m_typeBCountLabel, 6, 1);
    
    layout->addWidget(new QLabel("满意度:"), 7, 0);
    m_typeBSatisfactionLabel = new QLabel("0.00%");
    layout->addWidget(m_typeBSatisfactionLabel, 7, 1);
    
    // Progress bar
    layout->addWidget(new QLabel("满意度进度:"), 8, 0, 1, 2);
    m_satisfactionProgress = new QProgressBar;
    m_satisfactionProgress->setRange(0, 100);
    layout->addWidget(m_satisfactionProgress, 9, 0, 1, 2);
}

void StatisticsPanel::updateStatistics(const ModelStatistics &stats)
{
    m_iterationLabel->setText(QString::number(stats.iteration));
    m_satisfactionLabel->setText(QString("%1%").arg(stats.satisfactionRate * 100, 0, 'f', 2));
    m_segregationLabel->setText(QString::number(stats.segregationIndex, 'f', 3));
    m_agentsLabel->setText(QString::number(stats.totalAgents));
    
    m_typeACountLabel->setText(QString::number(stats.typeACount));
    m_typeASatisfactionLabel->setText(QString("%1%").arg(stats.typeASatisfaction * 100, 0, 'f', 2));
    m_typeBCountLabel->setText(QString::number(stats.typeBCount));
    m_typeBSatisfactionLabel->setText(QString("%1%").arg(stats.typeBSatisfaction * 100, 0, 'f', 2));
    
    m_satisfactionProgress->setValue(static_cast<int>(stats.satisfactionRate * 100));
}
