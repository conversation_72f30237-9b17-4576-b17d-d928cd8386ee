#ifndef STATISTICSPANEL_H
#define STATISTICSPANEL_H

#include <QGroupBox>
#include <QLabel>
#include <QProgressBar>
#include <QGridLayout>
#include "AgentType.h"

/**
 * @brief Statistics display panel
 * Shows current simulation statistics
 */
class StatisticsPanel : public QGroupBox
{
    Q_OBJECT

public:
    explicit StatisticsPanel(QWidget *parent = nullptr);
    ~StatisticsPanel();

public slots:
    void updateStatistics(const ModelStatistics &stats);

private:
    void setupUI();
    
    // UI components
    QLabel *m_iterationLabel;
    QLabel *m_satisfactionLabel;
    QLabel *m_segregationLabel;
    QLabel *m_agentsLabel;
    
    QLabel *m_typeACountLabel;
    QLabel *m_typeASatisfactionLabel;
    QLabel *m_typeBCountLabel;
    QLabel *m_typeBSatisfactionLabel;
    
    QProgressBar *m_satisfactionProgress;
};

#endif // STATISTICSPANEL_H
