#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QDebug>
#include <QMessageBox>
#include <QSplashScreen>
#include <QPixmap>
#include <QTimer>

#include "MainWindow.h"
#include "ColorConfig.h"

#ifdef USE_CUDA
#include <cuda_runtime.h>
#endif

void setupApplication(QApplication &app)
{
    // Set application properties
    app.setApplicationName("SchellingModel");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("SchellingSimulation");
    app.setOrganizationDomain("schelling.simulation");
    
    // Set application style
    app.setStyle(QStyleFactory::create("Fusion"));
    
    // Set application icon (if available)
    // app.setWindowIcon(QIcon(":/icons/app.png"));
}

bool checkCudaAvailability()
{
#ifdef USE_CUDA
    int deviceCount = 0;
    cudaError_t error = cudaGetDeviceCount(&deviceCount);
    
    if (error != cudaSuccess) {
        qDebug() << "CUDA error:" << cudaGetErrorString(error);
        return false;
    }
    
    if (deviceCount == 0) {
        qDebug() << "No CUDA devices found";
        return false;
    }
    
    qDebug() << "Found" << deviceCount << "CUDA device(s)";
    
    // Get device properties
    for (int i = 0; i < deviceCount; ++i) {
        cudaDeviceProp prop;
        cudaGetDeviceProperties(&prop, i);
        qDebug() << "Device" << i << ":" << prop.name 
                 << "- Compute capability:" << prop.major << "." << prop.minor
                 << "- Memory:" << prop.totalGlobalMem / (1024*1024) << "MB";
    }
    
    return true;
#else
    qDebug() << "CUDA support not compiled in";
    return false;
#endif
}

void showSplashScreen(QApplication &app)
{
    // Create a simple splash screen
    QPixmap pixmap(400, 300);
    pixmap.fill(QColor(52, 152, 219));
    
    QPainter painter(&pixmap);
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 16, QFont::Bold));
    painter.drawText(pixmap.rect(), Qt::AlignCenter, 
                    "Schelling隔离模型\n\nC++ Qt版本\n\n正在启动...");
    
    QSplashScreen splash(pixmap);
    splash.show();
    app.processEvents();
    
    // Simulate loading time
    QTimer::singleShot(1500, &splash, &QSplashScreen::close);
    
    // Keep splash visible for a short time
    QEventLoop loop;
    QTimer::singleShot(1500, &loop, &QEventLoop::quit);
    loop.exec();
}

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Setup application
    setupApplication(app);
    
    // Show splash screen
    showSplashScreen(app);
    
    // Check CUDA availability
    bool cudaAvailable = checkCudaAvailability();
    if (cudaAvailable) {
        qDebug() << "GPU acceleration available";
    } else {
        qDebug() << "Using CPU-only mode";
    }
    
    try {
        // Initialize color configuration
        ColorConfig::instance();
        
        // Create and show main window
        MainWindow window;
        window.show();
        
        qDebug() << "Application started successfully";
        
        return app.exec();
        
    } catch (const std::exception &e) {
        QMessageBox::critical(nullptr, "启动错误", 
                            QString("程序启动失败: %1").arg(e.what()));
        return -1;
    } catch (...) {
        QMessageBox::critical(nullptr, "启动错误", "程序启动失败: 未知错误");
        return -1;
    }
}
