"""
Schelling's Segregation Model Implementation
"""
import random
import numpy as np
from typing import List, Tu<PERSON>, Optional
from enum import Enum


class AgentType(Enum):
    """代理类型枚举"""
    EMPTY = 0
    TYPE_A = 1
    TYPE_B = 2


class Agent:
    """代理类，表示网格中的个体"""
    
    def __init__(self, agent_type: AgentType, x: int, y: int):
        self.type = agent_type
        self.x = x
        self.y = y
        self.satisfied = False
    
    def __repr__(self):
        return f"Agent({self.type}, {self.x}, {self.y}, satisfied={self.satisfied})"


class SchellingModel:
    """Schelling隔离模型主类"""
    
    def __init__(self, width: int = 50, height: int = 50, 
                 density: float = 0.8, minority_pc: float = 0.3, 
                 homophily: float = 0.3):
        """
        初始化Schelling模型
        
        Args:
            width: 网格宽度
            height: 网格高度
            density: 人口密度 (0-1)
            minority_pc: 少数群体比例 (0-1)
            homophily: 相似性阈值 (0-1)，代理满意所需的同类邻居比例
        """
        self.width = width
        self.height = height
        self.density = density
        self.minority_pc = minority_pc
        self.homophily = homophily
        
        # 初始化网格
        self.grid = np.full((height, width), AgentType.EMPTY, dtype=object)
        self.agents = []
        self.empty_cells = []
        
        # 统计信息
        self.iteration = 0
        self.total_agents = 0
        self.satisfied_agents = 0
        self.segregation_index = 0.0
        
        self.initialize_population()
    
    def initialize_population(self):
        """初始化人口分布"""
        self.agents.clear()
        self.empty_cells.clear()
        self.grid.fill(AgentType.EMPTY)
        
        # 计算总人口数
        total_population = int(self.width * self.height * self.density)
        minority_count = int(total_population * self.minority_pc)
        majority_count = total_population - minority_count
        
        # 创建所有可能的位置
        all_positions = [(x, y) for x in range(self.width) for y in range(self.height)]
        random.shuffle(all_positions)
        
        # 放置代理
        position_idx = 0
        
        # 放置少数群体
        for _ in range(minority_count):
            x, y = all_positions[position_idx]
            agent = Agent(AgentType.TYPE_A, x, y)
            self.grid[y, x] = agent
            self.agents.append(agent)
            position_idx += 1
        
        # 放置多数群体
        for _ in range(majority_count):
            x, y = all_positions[position_idx]
            agent = Agent(AgentType.TYPE_B, x, y)
            self.grid[y, x] = agent
            self.agents.append(agent)
            position_idx += 1
        
        # 记录空位置
        for i in range(position_idx, len(all_positions)):
            self.empty_cells.append(all_positions[i])
        
        self.total_agents = len(self.agents)
        self.iteration = 0
        self.update_satisfaction()
    
    def get_neighbors(self, x: int, y: int) -> List[Agent]:
        """获取指定位置的邻居（Moore邻域）"""
        neighbors = []
        for dx in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                if dx == 0 and dy == 0:
                    continue
                
                nx, ny = x + dx, y + dy
                if 0 <= nx < self.width and 0 <= ny < self.height:
                    cell = self.grid[ny, nx]
                    if isinstance(cell, Agent):
                        neighbors.append(cell)
        
        return neighbors
    
    def calculate_satisfaction(self, agent: Agent) -> bool:
        """计算代理的满意度"""
        neighbors = self.get_neighbors(agent.x, agent.y)
        
        if not neighbors:  # 没有邻居的情况
            return True
        
        same_type_count = sum(1 for neighbor in neighbors if neighbor.type == agent.type)
        satisfaction_ratio = same_type_count / len(neighbors)
        
        return satisfaction_ratio >= self.homophily
    
    def update_satisfaction(self):
        """更新所有代理的满意度"""
        self.satisfied_agents = 0
        for agent in self.agents:
            agent.satisfied = self.calculate_satisfaction(agent)
            if agent.satisfied:
                self.satisfied_agents += 1
        
        self.calculate_segregation_index()
    
    def calculate_segregation_index(self):
        """计算隔离指数"""
        if not self.agents:
            self.segregation_index = 0.0
            return
        
        type_a_agents = [a for a in self.agents if a.type == AgentType.TYPE_A]
        type_b_agents = [a for a in self.agents if a.type == AgentType.TYPE_B]
        
        if not type_a_agents or not type_b_agents:
            self.segregation_index = 1.0
            return
        
        # 计算每个群体的平均同类邻居比例
        total_same_ratio_a = 0
        total_same_ratio_b = 0
        
        for agent in type_a_agents:
            neighbors = self.get_neighbors(agent.x, agent.y)
            if neighbors:
                same_ratio = sum(1 for n in neighbors if n.type == agent.type) / len(neighbors)
                total_same_ratio_a += same_ratio
        
        for agent in type_b_agents:
            neighbors = self.get_neighbors(agent.x, agent.y)
            if neighbors:
                same_ratio = sum(1 for n in neighbors if n.type == agent.type) / len(neighbors)
                total_same_ratio_b += same_ratio
        
        avg_same_ratio_a = total_same_ratio_a / len(type_a_agents) if type_a_agents else 0
        avg_same_ratio_b = total_same_ratio_b / len(type_b_agents) if type_b_agents else 0
        
        self.segregation_index = (avg_same_ratio_a + avg_same_ratio_b) / 2
    
    def step(self) -> bool:
        """执行一步仿真"""
        if not self.empty_cells:
            return False  # 没有空位置，无法移动
        
        # 找到不满意的代理
        unsatisfied_agents = [agent for agent in self.agents if not agent.satisfied]
        
        if not unsatisfied_agents:
            return False  # 所有代理都满意，仿真结束
        
        # 随机选择一个不满意的代理进行移动
        agent_to_move = random.choice(unsatisfied_agents)
        
        # 随机选择一个空位置
        new_position = random.choice(self.empty_cells)
        
        # 移动代理
        self.move_agent(agent_to_move, new_position[0], new_position[1])
        
        self.iteration += 1
        self.update_satisfaction()
        
        return True
    
    def move_agent(self, agent: Agent, new_x: int, new_y: int):
        """移动代理到新位置"""
        # 清空原位置
        self.grid[agent.y, agent.x] = AgentType.EMPTY
        self.empty_cells.append((agent.x, agent.y))
        
        # 移动到新位置
        self.grid[new_y, new_x] = agent
        self.empty_cells.remove((new_x, new_y))
        
        # 更新代理坐标
        agent.x = new_x
        agent.y = new_y
    
    def get_statistics(self) -> dict:
        """获取统计信息"""
        satisfaction_rate = self.satisfied_agents / self.total_agents if self.total_agents > 0 else 0

        # 计算各群体的统计信息
        type_a_agents = [a for a in self.agents if a.type == AgentType.TYPE_A]
        type_b_agents = [a for a in self.agents if a.type == AgentType.TYPE_B]

        type_a_satisfied = sum(1 for a in type_a_agents if a.satisfied)
        type_b_satisfied = sum(1 for a in type_b_agents if a.satisfied)

        type_a_satisfaction = type_a_satisfied / len(type_a_agents) if type_a_agents else 0
        type_b_satisfaction = type_b_satisfied / len(type_b_agents) if type_b_agents else 0

        return {
            'iteration': self.iteration,
            'total_agents': self.total_agents,
            'satisfied_agents': self.satisfied_agents,
            'satisfaction_rate': satisfaction_rate,
            'segregation_index': self.segregation_index,
            'empty_cells': len(self.empty_cells),
            'type_a_count': len(type_a_agents),
            'type_b_count': len(type_b_agents),
            'type_a_satisfaction': type_a_satisfaction,
            'type_b_satisfaction': type_b_satisfaction,
            'density_actual': self.total_agents / (self.width * self.height),
            'minority_ratio_actual': len(type_a_agents) / self.total_agents if self.total_agents > 0 else 0
        }
    
    def reset(self):
        """重置模型"""
        self.initialize_population()
