"""
GPU加速版本的Schelling隔离模型
使用CuPy进行CUDA加速计算
"""
import time
import random
import numpy as np
from typing import List, Tuple, Optional
from enum import Enum

try:
    import cupy as cp
    GPU_AVAILABLE = True
    print("GPU加速可用 - 使用CuPy")
except ImportError:
    import numpy as cp
    GPU_AVAILABLE = False
    print("GPU不可用 - 回退到NumPy")


class AgentType(Enum):
    """代理类型枚举"""
    EMPTY = 0
    TYPE_A = 1
    TYPE_B = 2


class SchellingModelGPU:
    """GPU加速的Schelling隔离模型"""
    
    def __init__(self, width: int = 50, height: int = 50, 
                 density: float = 0.8, minority_pc: float = 0.3, 
                 homophily: float = 0.3, use_gpu: bool = True):
        """
        初始化GPU加速的Schelling模型
        
        Args:
            width: 网格宽度
            height: 网格高度
            density: 人口密度 (0-1)
            minority_pc: 少数群体比例 (0-1)
            homophily: 相似性阈值 (0-1)
            use_gpu: 是否使用GPU加速
        """
        self.width = width
        self.height = height
        self.density = density
        self.minority_pc = minority_pc
        self.homophily = homophily
        self.use_gpu = use_gpu and GPU_AVAILABLE
        
        # 选择计算后端
        self.xp = cp if self.use_gpu else np
        
        # 初始化网格 - 使用数值表示而不是对象
        # 0: 空, 1: TYPE_A, 2: TYPE_B
        self.grid = self.xp.zeros((height, width), dtype=self.xp.int32)
        self.satisfaction = self.xp.zeros((height, width), dtype=self.xp.bool_)
        
        # 统计信息
        self.iteration = 0
        self.total_agents = 0
        self.satisfied_agents = 0
        self.segregation_index = 0.0
        
        # 性能监控
        self.last_step_time = 0.0
        self.avg_step_time = 0.0
        
        # 预计算邻居偏移量
        self.neighbor_offsets = self.xp.array([
            [-1, -1], [-1, 0], [-1, 1],
            [0, -1],           [0, 1],
            [1, -1],  [1, 0],  [1, 1]
        ], dtype=self.xp.int32)
        
        self.initialize_population()
    
    def initialize_population(self):
        """初始化人口分布"""
        start_time = time.time()
        
        # 重置网格
        self.grid.fill(0)
        
        # 计算总人口数
        total_population = int(self.width * self.height * self.density)
        minority_count = int(total_population * self.minority_pc)
        majority_count = total_population - minority_count
        
        # 创建所有可能的位置
        positions = []
        for y in range(self.height):
            for x in range(self.width):
                positions.append((y, x))
        
        random.shuffle(positions)
        
        # 放置代理
        position_idx = 0
        
        # 放置少数群体 (TYPE_A = 1)
        for _ in range(minority_count):
            y, x = positions[position_idx]
            self.grid[y, x] = 1
            position_idx += 1
        
        # 放置多数群体 (TYPE_B = 2)
        for _ in range(majority_count):
            y, x = positions[position_idx]
            self.grid[y, x] = 2
            position_idx += 1
        
        self.total_agents = minority_count + majority_count
        self.iteration = 0
        
        # 计算初始满意度
        self.update_satisfaction()
        
        init_time = time.time() - start_time
        print(f"初始化完成 - 用时: {init_time:.3f}s ({'GPU' if self.use_gpu else 'CPU'})")
    
    def get_neighbor_counts(self):
        """GPU加速的邻居计算"""
        if self.use_gpu:
            return self._get_neighbor_counts_gpu()
        else:
            return self._get_neighbor_counts_cpu()
    
    def _get_neighbor_counts_gpu(self):
        """使用GPU计算邻居数量"""
        # 创建结果数组
        same_neighbors = self.xp.zeros((self.height, self.width), dtype=self.xp.int32)
        total_neighbors = self.xp.zeros((self.height, self.width), dtype=self.xp.int32)

        # 对每个邻居偏移量进行计算
        for dy, dx in self.neighbor_offsets:
            # 计算邻居位置 - 创建完整的坐标网格
            y_coords, x_coords = self.xp.meshgrid(
                self.xp.arange(self.height),
                self.xp.arange(self.width),
                indexing='ij'
            )
            neighbor_y = y_coords + dy
            neighbor_x = x_coords + dx

            # 检查边界
            valid_mask = ((neighbor_y >= 0) & (neighbor_y < self.height) &
                         (neighbor_x >= 0) & (neighbor_x < self.width))

            # 获取有效邻居的值
            neighbor_values = self.xp.zeros((self.height, self.width), dtype=self.xp.int32)
            # 使用where来安全地获取邻居值
            neighbor_values = self.xp.where(
                valid_mask,
                self.grid[neighbor_y, neighbor_x],
                0
            )

            # 计算邻居统计
            has_neighbor = valid_mask & (neighbor_values > 0)
            same_type = has_neighbor & (neighbor_values == self.grid)

            total_neighbors += has_neighbor.astype(self.xp.int32)
            same_neighbors += same_type.astype(self.xp.int32)

        return same_neighbors, total_neighbors
    
    def _get_neighbor_counts_cpu(self):
        """使用CPU计算邻居数量（回退方案）"""
        same_neighbors = np.zeros((self.height, self.width), dtype=np.int32)
        total_neighbors = np.zeros((self.height, self.width), dtype=np.int32)
        
        for y in range(self.height):
            for x in range(self.width):
                if self.grid[y, x] == 0:  # 空位置跳过
                    continue
                
                agent_type = self.grid[y, x]
                same_count = 0
                total_count = 0
                
                # 检查8个邻居
                for dy in [-1, 0, 1]:
                    for dx in [-1, 0, 1]:
                        if dy == 0 and dx == 0:
                            continue
                        
                        ny, nx = y + dy, x + dx
                        if 0 <= ny < self.height and 0 <= nx < self.width:
                            neighbor_type = self.grid[ny, nx]
                            if neighbor_type > 0:  # 有代理
                                total_count += 1
                                if neighbor_type == agent_type:
                                    same_count += 1
                
                same_neighbors[y, x] = same_count
                total_neighbors[y, x] = total_count
        
        return same_neighbors, total_neighbors
    
    def update_satisfaction(self):
        """更新所有代理的满意度"""
        start_time = time.time()
        
        # 获取邻居统计
        same_neighbors, total_neighbors = self.get_neighbor_counts()
        
        # 计算满意度
        # 只有有代理的位置才计算满意度
        agent_mask = self.grid > 0
        
        # 避免除零错误
        satisfaction_ratio = self.xp.zeros((self.height, self.width), dtype=self.xp.float32)
        valid_neighbors = total_neighbors > 0
        
        satisfaction_ratio[valid_neighbors] = (
            same_neighbors[valid_neighbors].astype(self.xp.float32) / 
            total_neighbors[valid_neighbors].astype(self.xp.float32)
        )
        
        # 没有邻居的代理默认满意
        no_neighbors = agent_mask & (total_neighbors == 0)
        satisfaction_ratio[no_neighbors] = 1.0
        
        # 更新满意度
        self.satisfaction = agent_mask & (satisfaction_ratio >= self.homophily)
        
        # 统计满意的代理数量
        if self.use_gpu:
            self.satisfied_agents = int(self.xp.sum(self.satisfaction).get())
        else:
            self.satisfied_agents = int(self.xp.sum(self.satisfaction))
        
        # 计算隔离指数
        self.calculate_segregation_index()
        
        calc_time = time.time() - start_time
        self.last_step_time = calc_time
    
    def calculate_segregation_index(self):
        """计算隔离指数"""
        if self.total_agents == 0:
            self.segregation_index = 0.0
            return
        
        # 分别计算两个群体的平均同类邻居比例
        type_a_mask = self.grid == 1
        type_b_mask = self.grid == 2
        
        same_neighbors, total_neighbors = self.get_neighbor_counts()
        
        # 计算各群体的平均同类邻居比例
        def calc_avg_same_ratio(type_mask):
            if self.use_gpu:
                count = self.xp.sum(type_mask).get()
            else:
                count = self.xp.sum(type_mask)
            
            if count == 0:
                return 0.0
            
            valid_agents = type_mask & (total_neighbors > 0)
            if self.use_gpu:
                valid_count = self.xp.sum(valid_agents).get()
            else:
                valid_count = self.xp.sum(valid_agents)
            
            if valid_count == 0:
                return 1.0
            
            ratios = same_neighbors[valid_agents].astype(self.xp.float32) / total_neighbors[valid_agents].astype(self.xp.float32)
            if self.use_gpu:
                return float(self.xp.mean(ratios).get())
            else:
                return float(self.xp.mean(ratios))
        
        avg_ratio_a = calc_avg_same_ratio(type_a_mask)
        avg_ratio_b = calc_avg_same_ratio(type_b_mask)
        
        self.segregation_index = (avg_ratio_a + avg_ratio_b) / 2
    
    def step(self) -> bool:
        """执行一步仿真"""
        start_time = time.time()
        
        # 找到不满意的代理
        unsatisfied_mask = (self.grid > 0) & (~self.satisfaction)
        
        if self.use_gpu:
            unsatisfied_count = int(self.xp.sum(unsatisfied_mask).get())
        else:
            unsatisfied_count = int(self.xp.sum(unsatisfied_mask))
        
        if unsatisfied_count == 0:
            return False  # 所有代理都满意
        
        # 找到空位置
        empty_mask = self.grid == 0
        if self.use_gpu:
            empty_count = int(self.xp.sum(empty_mask).get())
        else:
            empty_count = int(self.xp.sum(empty_mask))
        
        if empty_count == 0:
            return False  # 没有空位置
        
        # 随机选择一个不满意的代理和一个空位置
        if self.use_gpu:
            unsatisfied_positions = self.xp.where(unsatisfied_mask)
            empty_positions = self.xp.where(empty_mask)
            
            # 转换为CPU进行随机选择
            unsatisfied_y = unsatisfied_positions[0].get()
            unsatisfied_x = unsatisfied_positions[1].get()
            empty_y = empty_positions[0].get()
            empty_x = empty_positions[1].get()
        else:
            unsatisfied_positions = self.xp.where(unsatisfied_mask)
            empty_positions = self.xp.where(empty_mask)
            
            unsatisfied_y = unsatisfied_positions[0]
            unsatisfied_x = unsatisfied_positions[1]
            empty_y = empty_positions[0]
            empty_x = empty_positions[1]
        
        # 随机选择
        agent_idx = random.randint(0, len(unsatisfied_y) - 1)
        empty_idx = random.randint(0, len(empty_y) - 1)
        
        agent_y, agent_x = unsatisfied_y[agent_idx], unsatisfied_x[agent_idx]
        new_y, new_x = empty_y[empty_idx], empty_x[empty_idx]
        
        # 移动代理
        agent_type = self.grid[agent_y, agent_x]
        self.grid[new_y, new_x] = agent_type
        self.grid[agent_y, agent_x] = 0
        
        self.iteration += 1
        self.update_satisfaction()
        
        step_time = time.time() - start_time
        self.last_step_time = step_time
        
        # 更新平均步骤时间
        if self.avg_step_time == 0:
            self.avg_step_time = step_time
        else:
            self.avg_step_time = 0.9 * self.avg_step_time + 0.1 * step_time
        
        return True
    
    def get_grid_cpu(self):
        """获取CPU版本的网格数据（用于显示）"""
        if self.use_gpu:
            return self.grid.get()
        else:
            return self.grid
    
    def get_satisfaction_cpu(self):
        """获取CPU版本的满意度数据（用于显示）"""
        if self.use_gpu:
            return self.satisfaction.get()
        else:
            return self.satisfaction

    def get_statistics(self) -> dict:
        """获取统计信息"""
        satisfaction_rate = self.satisfied_agents / self.total_agents if self.total_agents > 0 else 0

        # 计算各群体的统计信息
        if self.use_gpu:
            type_a_count = int(self.xp.sum(self.grid == 1).get())
            type_b_count = int(self.xp.sum(self.grid == 2).get())
            type_a_satisfied = int(self.xp.sum((self.grid == 1) & self.satisfaction).get())
            type_b_satisfied = int(self.xp.sum((self.grid == 2) & self.satisfaction).get())
        else:
            type_a_count = int(self.xp.sum(self.grid == 1))
            type_b_count = int(self.xp.sum(self.grid == 2))
            type_a_satisfied = int(self.xp.sum((self.grid == 1) & self.satisfaction))
            type_b_satisfied = int(self.xp.sum((self.grid == 2) & self.satisfaction))

        type_a_satisfaction = type_a_satisfied / type_a_count if type_a_count > 0 else 0
        type_b_satisfaction = type_b_satisfied / type_b_count if type_b_count > 0 else 0

        return {
            'iteration': self.iteration,
            'total_agents': self.total_agents,
            'satisfied_agents': self.satisfied_agents,
            'satisfaction_rate': satisfaction_rate,
            'segregation_index': self.segregation_index,
            'empty_cells': self.width * self.height - self.total_agents,
            'type_a_count': type_a_count,
            'type_b_count': type_b_count,
            'type_a_satisfaction': type_a_satisfaction,
            'type_b_satisfaction': type_b_satisfaction,
            'density_actual': self.total_agents / (self.width * self.height),
            'minority_ratio_actual': type_a_count / self.total_agents if self.total_agents > 0 else 0,
            'last_step_time': self.last_step_time,
            'avg_step_time': self.avg_step_time,
            'using_gpu': self.use_gpu,
            'fps': 1.0 / self.avg_step_time if self.avg_step_time > 0 else 0
        }

    def reset(self):
        """重置模型"""
        self.initialize_population()

    def switch_backend(self, use_gpu: bool):
        """切换计算后端"""
        if use_gpu and not GPU_AVAILABLE:
            print("GPU不可用，继续使用CPU")
            return False

        if use_gpu == self.use_gpu:
            return True

        # 保存当前状态
        current_grid = self.get_grid_cpu()
        current_satisfaction = self.get_satisfaction_cpu()

        # 切换后端
        old_backend = "GPU" if self.use_gpu else "CPU"
        self.use_gpu = use_gpu
        self.xp = cp if self.use_gpu else np
        new_backend = "GPU" if self.use_gpu else "CPU"

        # 迁移数据
        self.grid = self.xp.array(current_grid, dtype=self.xp.int32)
        self.satisfaction = self.xp.array(current_satisfaction, dtype=self.xp.bool_)

        # 重新创建邻居偏移量
        self.neighbor_offsets = self.xp.array([
            [-1, -1], [-1, 0], [-1, 1],
            [0, -1],           [0, 1],
            [1, -1],  [1, 0],  [1, 1]
        ], dtype=self.xp.int32)

        print(f"计算后端已从{old_backend}切换到{new_backend}")
        return True
