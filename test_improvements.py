"""
测试界面改进和性能优化
"""
import time
from color_config import color_config
from schelling_model_gpu import SchellingModelGPU

def test_color_schemes():
    """测试新的颜色方案"""
    print("=== 测试新颜色方案 ===")
    print()
    
    presets = color_config.get_preset_names()
    
    for preset_key, preset_name in presets.items():
        print(f"方案: {preset_name} ({preset_key})")
        color_config.load_preset(preset_key)
        
        colors = color_config.get_all_colors()
        color_names = color_config.get_color_names()
        
        for color_type, color in colors.items():
            name = color_names[color_type]
            print(f"  {name}: RGB({color.red():3d}, {color.green():3d}, {color.blue():3d})")
        print()
    
    # 恢复默认
    color_config.reset_to_default()

def test_performance_with_display_control():
    """测试显示控制对性能的影响"""
    print("=== 测试显示控制性能影响 ===")
    print()
    
    # 创建模型
    model = SchellingModelGPU(width=50, height=50, density=0.8, use_gpu=True)
    
    # 模拟界面显示开启的情况
    print("模拟界面显示开启:")
    start_time = time.time()
    steps_with_display = 0
    
    for _ in range(100):
        if not model.step():
            break
        steps_with_display += 1
        # 模拟界面更新的开销
        time.sleep(0.001)  # 模拟1ms的界面更新时间
    
    time_with_display = time.time() - start_time
    fps_with_display = steps_with_display / time_with_display
    
    print(f"  步数: {steps_with_display}")
    print(f"  总时间: {time_with_display:.2f}s")
    print(f"  FPS: {fps_with_display:.1f}")
    print()
    
    # 重置模型
    model.reset()
    
    # 模拟界面显示关闭的情况
    print("模拟界面显示关闭:")
    start_time = time.time()
    steps_without_display = 0
    
    for _ in range(100):
        if not model.step():
            break
        steps_without_display += 1
        # 不进行界面更新
    
    time_without_display = time.time() - start_time
    fps_without_display = steps_without_display / time_without_display
    
    print(f"  步数: {steps_without_display}")
    print(f"  总时间: {time_without_display:.2f}s")
    print(f"  FPS: {fps_without_display:.1f}")
    print()
    
    # 计算性能提升
    if fps_with_display > 0:
        speedup = fps_without_display / fps_with_display
        print(f"关闭界面显示的性能提升: {speedup:.1f}x")
    print()

def test_gpu_optimization():
    """测试GPU优化效果"""
    print("=== 测试GPU优化效果 ===")
    print()
    
    grid_sizes = [(30, 30), (50, 50), (100, 100)]
    
    for width, height in grid_sizes:
        print(f"网格大小: {width}x{height}")
        print("-" * 30)
        
        # GPU版本
        model_gpu = SchellingModelGPU(width=width, height=height, use_gpu=True)
        start_time = time.time()
        
        for _ in range(50):
            if not model_gpu.step():
                break
        
        gpu_time = time.time() - start_time
        gpu_fps = 50 / gpu_time if gpu_time > 0 else 0
        
        print(f"GPU: {gpu_fps:.1f} FPS ({gpu_time:.3f}s)")
        
        # CPU版本
        model_cpu = SchellingModelGPU(width=width, height=height, use_gpu=False)
        start_time = time.time()
        
        for _ in range(50):
            if not model_cpu.step():
                break
        
        cpu_time = time.time() - start_time
        cpu_fps = 50 / cpu_time if cpu_time > 0 else 0
        
        print(f"CPU: {cpu_fps:.1f} FPS ({cpu_time:.3f}s)")
        
        if cpu_fps > 0:
            speedup = gpu_fps / cpu_fps
            print(f"GPU加速比: {speedup:.2f}x")
        
        print()

if __name__ == "__main__":
    test_color_schemes()
    test_performance_with_display_control()
    test_gpu_optimization()
