"""
测试大网格功能
"""
import time
import psutil
import os
from schelling_model_gpu import SchellingModelGPU

def get_memory_usage():
    """获取当前内存使用量(MB)"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

def test_large_grids():
    """测试不同大小的网格"""
    print("=== 大网格测试 ===")
    print()
    
    # 测试不同的网格大小
    grid_sizes = [
        (100, 100),      # 1万个单元格
        (316, 316),      # 10万个单元格
        (1000, 1000),    # 100万个单元格
        (3162, 3162),    # 1000万个单元格
        (10000, 10000),  # 1亿个单元格
    ]
    
    for width, height in grid_sizes:
        total_cells = width * height
        print(f"测试网格大小: {width}×{height} ({total_cells:,}个单元格)")
        print("-" * 50)
        
        try:
            # 记录初始内存
            initial_memory = get_memory_usage()
            
            # 创建模型
            start_time = time.time()
            model = SchellingModelGPU(
                width=width, height=height,
                density=0.1,  # 降低密度以减少计算量
                minority_pc=0.3,
                homophily=0.3,
                use_gpu=True
            )
            init_time = time.time() - start_time
            
            # 记录创建后内存
            after_init_memory = get_memory_usage()
            memory_used = after_init_memory - initial_memory
            
            print(f"✅ 初始化成功")
            print(f"   初始化时间: {init_time:.2f}s")
            print(f"   内存使用: {memory_used:.1f}MB")
            print(f"   代理数量: {model.total_agents:,}")
            
            # 测试几步仿真
            step_times = []
            for i in range(5):
                step_start = time.time()
                if not model.step():
                    print(f"   仿真在第{i+1}步达到平衡")
                    break
                step_time = time.time() - step_start
                step_times.append(step_time)
            
            if step_times:
                avg_step_time = sum(step_times) / len(step_times)
                print(f"   平均步骤时间: {avg_step_time:.3f}s")
                print(f"   预估FPS: {1/avg_step_time:.1f}")
            
            # 清理内存
            del model
            
        except MemoryError:
            print(f"❌ 内存不足，无法创建{total_cells:,}个单元格的网格")
        except Exception as e:
            print(f"❌ 创建失败: {e}")
        
        print()

def test_memory_efficiency():
    """测试内存效率"""
    print("=== 内存效率测试 ===")
    print()
    
    sizes = [(100, 100), (500, 500), (1000, 1000)]
    
    for width, height in sizes:
        total_cells = width * height
        
        print(f"网格大小: {width}×{height}")
        
        # 理论内存使用
        theoretical_memory = total_cells * 13 / (1024 * 1024)  # 13字节每单元格
        print(f"理论内存: {theoretical_memory:.1f}MB")
        
        # 实际测试
        initial_memory = get_memory_usage()
        
        try:
            model = SchellingModelGPU(width=width, height=height, use_gpu=True)
            actual_memory = get_memory_usage() - initial_memory
            
            print(f"实际内存: {actual_memory:.1f}MB")
            print(f"内存效率: {theoretical_memory/actual_memory*100:.1f}%")
            
            del model
            
        except Exception as e:
            print(f"测试失败: {e}")
        
        print()

def benchmark_gpu_vs_cpu():
    """GPU vs CPU 性能对比"""
    print("=== GPU vs CPU 大网格性能对比 ===")
    print()
    
    width, height = 1000, 1000
    print(f"网格大小: {width}×{height}")
    print()
    
    # GPU测试
    try:
        print("GPU测试:")
        model_gpu = SchellingModelGPU(width=width, height=height, use_gpu=True, density=0.1)
        
        start_time = time.time()
        for _ in range(10):
            if not model_gpu.step():
                break
        gpu_time = time.time() - start_time
        
        print(f"  10步用时: {gpu_time:.2f}s")
        print(f"  平均每步: {gpu_time/10:.3f}s")
        
        del model_gpu
        
    except Exception as e:
        print(f"  GPU测试失败: {e}")
    
    # CPU测试
    try:
        print("CPU测试:")
        model_cpu = SchellingModelGPU(width=width, height=height, use_gpu=False, density=0.1)
        
        start_time = time.time()
        for _ in range(10):
            if not model_cpu.step():
                break
        cpu_time = time.time() - start_time
        
        print(f"  10步用时: {cpu_time:.2f}s")
        print(f"  平均每步: {cpu_time/10:.3f}s")
        
        if gpu_time > 0:
            speedup = cpu_time / gpu_time
            print(f"  GPU加速比: {speedup:.2f}x")
        
        del model_cpu
        
    except Exception as e:
        print(f"  CPU测试失败: {e}")

if __name__ == "__main__":
    print(f"当前内存使用: {get_memory_usage():.1f}MB")
    print()
    
    test_memory_efficiency()
    test_large_grids()
    benchmark_gpu_vs_cpu()
