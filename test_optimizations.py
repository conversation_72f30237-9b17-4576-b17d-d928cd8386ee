"""
测试优化效果
"""
import time
import numpy as np
from schelling_model_gpu import SchellingModelGPU
from color_config import color_config

def test_performance():
    """测试性能"""
    print("=== Schelling模型性能测试 ===")
    print()
    
    # 测试不同网格大小
    grid_sizes = [(30, 30), (50, 50), (100, 100)]
    
    for width, height in grid_sizes:
        print(f"测试网格大小: {width}x{height}")
        print("-" * 40)
        
        # 测试GPU版本
        print("GPU版本:")
        model_gpu = SchellingModelGPU(
            width=width, height=height,
            density=0.8, minority_pc=0.3, homophily=0.3,
            use_gpu=True
        )
        
        # 预热
        for _ in range(5):
            model_gpu.step()
        
        # 性能测试
        start_time = time.time()
        steps = 0
        test_duration = 3.0  # 测试3秒
        
        while time.time() - start_time < test_duration:
            if not model_gpu.step():
                break
            steps += 1
        
        elapsed = time.time() - start_time
        fps_gpu = steps / elapsed
        avg_step_time_gpu = elapsed / steps if steps > 0 else 0
        
        print(f"  步数: {steps}")
        print(f"  FPS: {fps_gpu:.1f}")
        print(f"  平均步骤时间: {avg_step_time_gpu*1000:.2f}ms")
        print(f"  总时间: {elapsed:.2f}s")
        
        # 测试CPU版本
        print("CPU版本:")
        model_cpu = SchellingModelGPU(
            width=width, height=height,
            density=0.8, minority_pc=0.3, homophily=0.3,
            use_gpu=False
        )
        
        # 预热
        for _ in range(5):
            model_cpu.step()
        
        # 性能测试
        start_time = time.time()
        steps = 0
        test_duration = 3.0  # 测试3秒
        
        while time.time() - start_time < test_duration:
            if not model_cpu.step():
                break
            steps += 1
        
        elapsed = time.time() - start_time
        fps_cpu = steps / elapsed
        avg_step_time_cpu = elapsed / steps if steps > 0 else 0
        
        print(f"  步数: {steps}")
        print(f"  FPS: {fps_cpu:.1f}")
        print(f"  平均步骤时间: {avg_step_time_cpu*1000:.2f}ms")
        print(f"  总时间: {elapsed:.2f}s")
        
        # 计算加速比
        if fps_cpu > 0:
            speedup = fps_gpu / fps_cpu
            print(f"GPU加速比: {speedup:.2f}x")
        
        print()

def test_color_config():
    """测试颜色配置"""
    print("=== 颜色配置测试 ===")
    print()
    
    # 测试默认颜色
    print("默认颜色配置:")
    colors = color_config.get_all_colors()
    for color_type, color in colors.items():
        name = color_config.get_color_names()[color_type]
        print(f"  {name}: RGB({color.red()}, {color.green()}, {color.blue()})")
    print()
    
    # 测试预设方案
    presets = color_config.get_preset_names()
    print("可用预设方案:")
    for key, name in presets.items():
        print(f"  {key}: {name}")
    print()
    
    # 测试切换预设
    print("测试切换到经典配色:")
    color_config.load_preset('classic')
    colors = color_config.get_all_colors()
    for color_type, color in colors.items():
        name = color_config.get_color_names()[color_type]
        print(f"  {name}: RGB({color.red()}, {color.green()}, {color.blue()})")
    
    # 恢复默认
    color_config.reset_to_default()
    print("\n已恢复默认配色")

if __name__ == "__main__":
    test_color_config()
    print()
    test_performance()
